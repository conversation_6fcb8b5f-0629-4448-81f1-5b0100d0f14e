// src/app/test-dispatched-vehicles/page.tsx
'use client';

import React, { useState } from 'react';
import { FixedVehicleDispatchContainer } from '@/features/vehicle-dispatch/components/VehicleDispatchContainer';

/**
 * 测试已出厂车辆表格的页面
 */
export default function TestDispatchedVehiclesPage() {
  return (
    <div className='h-screen flex bg-gray-100'>
      {/* 左侧任务列表区域（模拟） */}
      <div className='flex-1 p-4'>
        <div className='bg-white rounded-lg shadow h-full p-4'>
          <h2 className='text-lg font-semibold mb-4'>任务列表区域</h2>
          <div className='text-gray-500'>
            这里是任务列表的模拟区域。右侧是车辆调度模块，包含了新增的已出厂车辆表格。
          </div>

          <div className='mt-8 space-y-4'>
            <h3 className='text-md font-medium'>已出厂车辆表格功能：</h3>
            <ul className='list-disc list-inside space-y-2 text-sm text-gray-600'>
              <li>✅ 车号（带往返状态图标）</li>
              <li>✅ 司机信息</li>
              <li>✅ 出站时间</li>
              <li>✅ 到达时间</li>
              <li>✅ 工地时长</li>
              <li>✅ 返程时间</li>
              <li>✅ 去程时长</li>
              <li>✅ 返程时长</li>
              <li>✅ 总时长</li>
              <li>✅ 发货单编号</li>
              <li>✅ 列拖拽调整顺序</li>
              <li>✅ 横向滚动支持</li>
            </ul>
          </div>
        </div>
      </div>

      {/* 右侧车辆调度模块 */}
      <div className='w-80 p-4'>
        <div className='bg-white rounded-lg shadow h-full'>
          <FixedVehicleDispatchContainer gridColumns={4} />
        </div>
      </div>
    </div>
  );
}
