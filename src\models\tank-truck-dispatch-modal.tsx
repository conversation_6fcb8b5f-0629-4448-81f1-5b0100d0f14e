'use client';

import React, { useMemo, useCallback, useState } from 'react';

import { Save, Truck, CheckCircle2, Plus } from 'lucide-react';

import { EditableComboBox } from '@/core/components/editable-combo-box';
import { PumpTruckDispatchModal } from '@/models/pump-truck-dispatch-modal';
import { Button } from '@/shared/components/button';
import { Checkbox } from '@/shared/components/checkbox';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/shared/components/dialog';
import { Input } from '@/shared/components/input';
import { Label } from '@/shared/components/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/select';
import type { Task, Vehicle } from '@/core/types';

interface TankTruckDispatchModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  task?: Partial<Task>;
  vehicle?: Partial<Vehicle>;
  onConfirm: (dispatchData: TankTruckDispatchData) => Promise<void>;
}

export interface TankTruckDispatchData {
  // 基础信息
  dispatchNumber: string;
  taskNumber: string;
  contractNumber: string;

  // 第二行字段
  materialType: string;
  mortarType: string;
  projectName: string;

  // 第三行字段
  stationNumber: string;
  mainOperator: string;
  constructionUnit: string;

  // 第四行字段
  vehicleNumber: string;
  driverName: string;
  constructionPart: string;

  // 第五行字段
  qualityInspector: string;
  outputTemperature: string;
  strengthGrade: string;
  slump: string; // 塌落度

  // 第六行字段
  pouringMethod: string;
  siteDispatcher: string;
  dispatcher: string;

  // 第七行字段 - 方量设定
  maxSetting: boolean;
  noOverload: boolean;
  adjustByWeight: boolean;
  volumeFrom: number; // 方量起始值
  volumeTo: number; // 方量结束值
  volumeLock: boolean; // 锁定
  volumeQuantity: number; // 量

  // 打印设定（独立区域）
  printVolume: number;
  printWeight: number;
  dispatchMethod: string;

  // 右侧设置
  productionTip: string;
  driverTip: string;
  printTip: string;

  // 补充字段
  prepaidBalance: string; // 预存余额
  notes: string; // 注释
  pumpTruckNumber: string; // 泵车号

  // 复选框选项
  autoDispatch: boolean;
  backSandMortar: boolean;
  washingWater: boolean;
  withPump: boolean;
  noMixingStation: boolean;
  allowHouseRepair: boolean;

  // 底部
  printTicket: boolean;
  ticketNote: string;

  // 车辆列表（支持多车发车）
  selectedVehicles: Array<{
    vehicleNumber: string;
    driverName: string;
    volume: number;
    overVolume?: string;
    overWeight?: string;
  }>;
}

export const TankTruckDispatchModal: React.FC<TankTruckDispatchModalProps> = ({
  open,
  onOpenChange,
  task,
  vehicle,
  onConfirm,
}) => {
  console.log('🚛 TankTruckDispatchModal render:', {
    open,
    task: task?.taskNumber,
    vehicle: vehicle?.vehicleNumber,
    hasOnConfirm: !!onConfirm,
  });
  const [formData, setFormData] = useState<TankTruckDispatchData>({
    // 基础信息
    dispatchNumber: '新发货单',
    taskNumber: task?.taskNumber || 'C125-00003',
    contractNumber: task?.contractNumber || 'C125-00002',

    // 第二行字段
    materialType: '砼',
    mortarType: '',
    projectName: task?.projectName || '个人自建',

    // 第三行字段
    stationNumber: '站点1',
    mainOperator: '',
    constructionUnit: task?.constructionUnit || '长治市郊善堂洗煤工程有限公司',

    // 第四行字段
    vehicleNumber: vehicle?.vehicleNumber || '10',
    driverName: vehicle?.driverName || '朱志国',
    constructionPart: '垫层',

    // 第五行字段
    qualityInspector: '',
    outputTemperature: '',
    strengthGrade: task?.strength || 'C15',

    // 第六行字段
    pouringMethod: '自卸',
    siteDispatcher: '',
    dispatcher: '系统管理员',

    // 第七行字段 - 方量设定
    maxSetting: true,
    noOverload: false,
    adjustByWeight: false,
    volumeFrom: 12,
    volumeTo: 12,
    volumeLock: false,
    volumeQuantity: 1,

    // 打印设定（独立区域）
    printVolume: 6.57,
    printWeight: 130.04,
    dispatchMethod: '生成发货单',

    // 右侧设置
    productionTip: '',
    driverTip: '',
    printTip: '',

    // 补充字段
    slump: '180±20', // 塌落度
    prepaidBalance: '0.00', // 预存余额
    notes: '', // 注释
    pumpTruckNumber: '', // 泵车号

    // 复选框选项
    autoDispatch: false,
    backSandMortar: false,
    washingWater: false,
    withPump: false,
    noMixingStation: false,
    allowHouseRepair: false,

    // 底部
    printTicket: false,
    ticketNote: '',

    // 车辆列表（支持多车发车）
    selectedVehicles: [
      {
        vehicleNumber: vehicle?.vehicleNumber || '10',
        driverName: vehicle?.driverName || '未志国',
        volume: 6.57,
        overVolume: '-',
        overWeight: '-',
      },
    ],
  });

  // 车辆选择模态框状态
  const [isVehicleSelectOpen, setIsVehicleSelectOpen] = useState(false);
  const [editingVehicleIndex, setEditingVehicleIndex] = useState<number | null>(null);

  // 泵车相关模态框状态
  const [isPumpTruckDispatchOpen, setIsPumpTruckDispatchOpen] = useState(false);
  const [isPumpTruckReturnOpen, setIsPumpTruckReturnOpen] = useState(false);

  // EditableComboBox 选项数据
  const [fieldOptions, setFieldOptions] = useState({
    driverName: ['朱志国', '张三', '李四', '王五', '赵六'],
    mainOperator: ['操作员1', '操作员2', '操作员3'],
    qualityInspector: ['质检员1', '质检员2', '质检员3'],
    outputTemperature: ['25°C', '30°C', '35°C'],
    siteDispatcher: ['调度员1', '调度员2', '调度员3'],
    notes: ['注意安全', '按时到达', '联系现场'],
    productionTip: ['正常生产', '加急生产', '优先生产'],
    driverTip: ['注意路况', '小心驾驶', '按时到达'],
    printTip: ['正常打印', '加急打印', '重复打印'],
  });

  // 模拟车辆数据
  const availableVehicles = [
    { vehicleNumber: '10', driverName: '朱志国', capacity: 12 },
    { vehicleNumber: '11', driverName: '张三', capacity: 12 },
    { vehicleNumber: '12', driverName: '李四', capacity: 10 },
    { vehicleNumber: '13', driverName: '王五', capacity: 15 },
    { vehicleNumber: '14', driverName: '赵六', capacity: 12 },
  ];

  // 货物类型和砂浆类型的联动选项
  const getMortarTypeOptions = (materialType: string) => {
    switch (materialType) {
      case '砼':
        return []; // 砼类型时砂浆类型不可选择
      case '砂浆':
        return ['接茬', '润管', '抹灰', '砌筑', '防水'];
      case '水票':
        return ['内部结算', '客户结算'];
      default:
        return [];
    }
  };

  // 获取砂浆类型的占位符文本
  const getMortarTypePlaceholder = (materialType: string) => {
    switch (materialType) {
      case '砼':
        return '不可选择';
      case '砂浆':
        return '请选择砂浆类型';
      case '水票':
        return '请选择结算方式';
      default:
        return '请选择类型';
    }
  };

  const updateField = (field: keyof TankTruckDispatchData, value: any) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value };

      // 当货物类型改变时，重置砂浆类型
      if (field === 'materialType') {
        newData.mortarType = '';
      }

      return newData;
    });
  };

  // 添加选项
  const addOption = (fieldName: keyof typeof fieldOptions, option: string) => {
    setFieldOptions(prev => ({
      ...prev,
      [fieldName]: [...prev[fieldName], option],
    }));
  };

  // 删除选项
  const removeOption = (fieldName: keyof typeof fieldOptions, option: string) => {
    setFieldOptions(prev => ({
      ...prev,
      [fieldName]: prev[fieldName].filter(item => item !== option),
    }));
  };

  const handleDispatch = async () => {
    console.log('💾 保存发车单并调出车辆:', formData);
    try {
      // 先保存发车单数据
      console.log('保存发车单:', formData);

      // 然后执行车辆调度
      await onConfirm(formData);
      console.log('✅ 车辆调度成功，关闭模态框');
      onOpenChange(false);
    } catch (error) {
      console.error('❌ 车辆调度失败:', error);
      // 不关闭模态框，让用户重试
    }
  };

  // 车辆相关处理函数
  const handleVehicleSelect = (vehicle: (typeof availableVehicles)[0]) => {
    if (editingVehicleIndex !== null && editingVehicleIndex < formData.selectedVehicles.length) {
      // 编辑现有车辆
      const newVehicles = [...formData.selectedVehicles];
      const existingVehicle = newVehicles[editingVehicleIndex];
      if (existingVehicle) {
        newVehicles[editingVehicleIndex] = {
          vehicleNumber: vehicle.vehicleNumber,
          driverName: vehicle.driverName,
          volume: existingVehicle.volume,
          overVolume: existingVehicle.overVolume,
          overWeight: existingVehicle.overWeight,
        };
        updateField('selectedVehicles', newVehicles);
      }
    } else {
      // 添加新车辆
      const newVehicle = {
        vehicleNumber: vehicle.vehicleNumber,
        driverName: vehicle.driverName,
        volume: 6.57,
        overVolume: '-',
        overWeight: '-',
      };
      updateField('selectedVehicles', [...formData.selectedVehicles, newVehicle]);
    }
    setIsVehicleSelectOpen(false);
    setEditingVehicleIndex(null);
  };

  const handleVehicleEdit = (index: number) => {
    setEditingVehicleIndex(index);
    setIsVehicleSelectOpen(true);
  };

  const handleVehicleRemove = (index: number) => {
    const newVehicles = formData.selectedVehicles.filter((_, i) => i !== index);
    updateField('selectedVehicles', newVehicles);
  };

  const handleClearAllVehicles = () => {
    updateField('selectedVehicles', []);
  };

  // 泵车相关处理函数
  const handlePumpTruckSelect = (option: string) => {
    if (option === '安排泵车') {
      setIsPumpTruckDispatchOpen(true);
    } else if (option === '返回泵车') {
      setIsPumpTruckReturnOpen(true);
    }
  };

  const handlePumpTruckDispatchComplete = (pumpTruckNumber: string) => {
    updateField('pumpTruckNumber', pumpTruckNumber);
    setIsPumpTruckDispatchOpen(false);
  };

  const handlePumpTruckReturnComplete = (pumpTruckNumber: string) => {
    updateField('pumpTruckNumber', pumpTruckNumber);
    setIsPumpTruckReturnOpen(false);
  };

  // 获取必填字段的边框样式
  const getRequiredFieldStyle = (fieldName: keyof TankTruckDispatchData, baseStyle: string) => {
    const value = formData[fieldName];
    const isEmpty = !value || (typeof value === 'string' && value.trim() === '');

    if (isEmpty) {
      return baseStyle.replace('border-slate-200', 'border-red-400');
    }
    return baseStyle;
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className='max-w-7xl max-h-[95vh] overflow-hidden p-0 [&>button]:hidden'>
          <DialogTitle className='sr-only'>罐车发车单</DialogTitle>
          <div className='p-0 px-4 py-0.5 bg-blue-500 text-white rounded-t-lg'>
            <div className='flex items-center justify-between text-base font-semibold'>
              <div className='flex items-center gap-2'>
                <div className='w-2 h-2  rounded-full'></div>
                <Truck className='w-4 h-4' />
                <span>罐车发车单</span>
              </div>
            </div>
          </div>

          <div className='max-h-[calc(95vh-80px)] overflow-y-auto px-4 space-y-2 bg-gradient-to-b from-transparent to-slate-50/50'>
            {/* 第一行：发货单编号、任务编号、合同编号 */}
            <div className='bg-white rounded-lg border border-slate-200 shadow-sm p-1'>
              <div className='grid grid-cols-3 gap-3'>
                <div className='flex items-center gap-1'>
                  <Label className='text-xs text-gray-600 whitespace-nowrap w-16'>
                    发货单编号：
                  </Label>
                  <Input
                    value={formData.dispatchNumber}
                    className='h-7 text-xs bg-slate-50 border-slate-200 text-slate-700 flex-1'
                    readOnly
                  />
                </div>
                <div className='flex items-center gap-1'>
                  <Label className='text-xs text-gray-600 whitespace-nowrap w-16'>任务编号：</Label>
                  <Input
                    value={formData.taskNumber}
                    className='h-7 text-xs bg-slate-50 border-slate-200 text-slate-700 flex-1'
                    readOnly
                  />
                </div>
                <div className='flex items-center gap-1'>
                  <Label className='text-xs text-gray-600 whitespace-nowrap w-16'>合同编号：</Label>
                  <Input
                    value={formData.contractNumber}
                    className='h-7 text-xs bg-slate-50 border-slate-200 text-slate-700 flex-1'
                    readOnly
                  />
                </div>
              </div>
            </div>

            {/* 第二行：货物类型、砂浆类型、工程名称 */}
            <div className='bg-white rounded-lg border border-slate-200 shadow-sm p-1'>
              <div className='grid grid-cols-3 gap-3'>
                <div className='flex items-center gap-1'>
                  <Label className='text-xs text-gray-600 whitespace-nowrap w-16'>货物类型：</Label>
                  <Select
                    value={formData.materialType}
                    onValueChange={v => updateField('materialType', v)}
                  >
                    <SelectTrigger
                      className={getRequiredFieldStyle(
                        'materialType',
                        'h-7 text-xs border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100 flex-1'
                      )}
                    >
                      <SelectValue placeholder='请选择货物类型' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='砼'>砼</SelectItem>
                      <SelectItem value='砂浆'>砂浆</SelectItem>
                      <SelectItem value='水票'>水票</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className='flex items-center gap-1'>
                  <Label className='text-xs text-gray-600 whitespace-nowrap w-16'>砂浆类型：</Label>
                  <Select
                    value={formData.mortarType}
                    onValueChange={v => updateField('mortarType', v)}
                    disabled={getMortarTypeOptions(formData.materialType).length === 0}
                  >
                    <SelectTrigger className='h-7 text-xs border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100 flex-1'>
                      <SelectValue placeholder={getMortarTypePlaceholder(formData.materialType)} />
                    </SelectTrigger>
                    <SelectContent>
                      {getMortarTypeOptions(formData.materialType).map(option => (
                        <SelectItem key={option} value={option}>
                          {option}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className='flex items-center gap-1'>
                  <Label className='text-xs text-gray-600 whitespace-nowrap w-16'>工程名称：</Label>
                  <Input
                    value={formData.projectName}
                    className='h-7 text-xs bg-slate-50 border-slate-200 text-slate-700 flex-1'
                    readOnly
                  />
                </div>
              </div>
            </div>

            {/* 第三行：站号、主机操作、建设单位 */}
            <div className='bg-white rounded-lg border border-slate-200 shadow-sm p-1'>
              <div className='grid grid-cols-3 gap-2'>
                <div className='flex items-center gap-1'>
                  <Label className='text-xs text-gray-600 whitespace-nowrap w-16'>站号：</Label>
                  <Select
                    value={formData.stationNumber}
                    onValueChange={v => updateField('stationNumber', v)}
                  >
                    <SelectTrigger
                      className={getRequiredFieldStyle(
                        'stationNumber',
                        'h-7 text-xs border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100 flex-1'
                      )}
                    >
                      <SelectValue placeholder='请选择站号' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='站点1'>站点1</SelectItem>
                      <SelectItem value='站点2'>站点2</SelectItem>
                      <SelectItem value='站点3'>站点3</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className='flex items-center gap-1'>
                  <Label className='text-xs text-gray-600 whitespace-nowrap w-16'>主机操作：</Label>
                  <EditableComboBox
                    value={formData.mainOperator}
                    onValueChange={v => updateField('mainOperator', v)}
                    options={fieldOptions.mainOperator}
                    onAddOption={option => addOption('mainOperator', option)}
                    onRemoveOption={option => removeOption('mainOperator', option)}
                    className='h-7 text-xs'
                    placeholder='请选择或输入主机操作'
                    allowAdd={true}
                    allowRemove={true}
                    addFromInput={true}
                  />
                </div>
                <div className='flex items-center gap-1'>
                  <Label className='text-xs text-gray-600 whitespace-nowrap w-16'>建设单位：</Label>
                  <Input
                    value={formData.constructionUnit}
                    className='h-7 text-xs bg-slate-50 border-slate-200 text-slate-700 flex-1'
                    readOnly
                  />
                </div>
              </div>
            </div>

            {/* 第四行：车号、司机、施工部位 */}
            <div className='bg-white rounded-lg border border-slate-200 shadow-sm p-1'>
              <div className='grid grid-cols-3 gap-2'>
                <div className='flex items-center gap-1'>
                  <Label className='text-xs text-gray-600 whitespace-nowrap w-16'>车号：</Label>
                  <Select
                    value={formData.vehicleNumber}
                    onValueChange={v => updateField('vehicleNumber', v)}
                  >
                    <SelectTrigger
                      className={getRequiredFieldStyle(
                        'vehicleNumber',
                        'h-7 text-xs border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100 flex-1'
                      )}
                    >
                      <SelectValue placeholder='请选择车号' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='10'>10</SelectItem>
                      <SelectItem value='11'>11</SelectItem>
                      <SelectItem value='12'>12</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className='flex items-center gap-1'>
                  <Label className='text-xs text-gray-600 whitespace-nowrap w-16'>司机：</Label>
                  <EditableComboBox
                    value={formData.driverName}
                    onValueChange={v => updateField('driverName', v)}
                    options={fieldOptions.driverName}
                    onAddOption={option => addOption('driverName', option)}
                    onRemoveOption={option => removeOption('driverName', option)}
                    className='h-7 text-xs'
                    placeholder='请选择或输入司机'
                    allowAdd={true}
                    allowRemove={true}
                    addFromInput={true}
                  />
                </div>
                <div className='flex items-center gap-1'>
                  <Label className='text-xs text-gray-600 whitespace-nowrap w-16'>施工部位：</Label>
                  <Input
                    value={formData.constructionPart}
                    className='h-7 text-xs bg-slate-50 border-slate-200 text-slate-700 flex-1'
                    readOnly
                  />
                </div>
              </div>
            </div>

            {/* 第五行：原检员、出机温度、强度等级、塌落度 */}
            <div className='bg-white rounded-lg border border-slate-200 shadow-sm p-1'>
              <div className='grid grid-cols-3 gap-1'>
                <div className='flex items-center gap-1'>
                  <Label className='text-xs text-gray-600 whitespace-nowrap w-16'>
                    质 检 员：{' '}
                  </Label>
                  <EditableComboBox
                    value={formData.qualityInspector}
                    onValueChange={v => updateField('qualityInspector', v)}
                    options={fieldOptions.qualityInspector}
                    onAddOption={option => addOption('qualityInspector', option)}
                    onRemoveOption={option => removeOption('qualityInspector', option)}
                    className='h-7 text-xs'
                    placeholder='请选择或输入质检员'
                    allowAdd={true}
                    allowRemove={true}
                    addFromInput={true}
                  />
                </div>
                <div className='flex items-center gap-1'>
                  <Label className='text-xs text-gray-600 whitespace-nowrap w-16'>出机温度：</Label>
                  <EditableComboBox
                    value={formData.outputTemperature}
                    onValueChange={v => updateField('outputTemperature', v)}
                    options={fieldOptions.outputTemperature}
                    onAddOption={option => addOption('outputTemperature', option)}
                    onRemoveOption={option => removeOption('outputTemperature', option)}
                    className='h-7 text-xs'
                    placeholder='请选择或输入出机温度'
                    allowAdd={true}
                    allowRemove={true}
                    addFromInput={true}
                  />
                </div>
                <div className='flex items-center gap-1'>
                  <div className='flex items-center gap-1'>
                    <Label className='text-xs text-gray-600 whitespace-nowrap w-16'>
                      强度等级：
                    </Label>
                    <Input
                      value={formData.strengthGrade}
                      className='h-7 text-xs bg-slate-50 border-slate-200 text-slate-700 flex-1'
                      readOnly
                    />
                  </div>
                  <div className='flex items-center gap-1'>
                    <Label className='text-xs text-gray-600 whitespace-nowrap w-16'>塌落度：</Label>
                    <Input
                      value={formData.slump}
                      onChange={e => updateField('slump', e.target.value)}
                      className='h-7 text-xs border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100 transition-colors flex-1'
                      placeholder='请输入塌落度'
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* 第六行：浇筑方式、现场调度、调度员、预存余额 */}
            <div className='bg-white rounded-lg border border-slate-200 shadow-sm p-1'>
              <div className='grid grid-cols-3 gap-2'>
                <div className='flex items-center gap-1'>
                  <Label className='text-xs text-gray-600 whitespace-nowrap w-16'>浇筑方式：</Label>
                  <Select
                    value={formData.pouringMethod}
                    onValueChange={v => updateField('pouringMethod', v)}
                  >
                    <SelectTrigger
                      className={getRequiredFieldStyle(
                        'pouringMethod',
                        'h-7 text-xs border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100 flex-1'
                      )}
                    >
                      <SelectValue placeholder='请选择浇筑方式' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='自卸'>自卸</SelectItem>
                      <SelectItem value='泵送'>泵送</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className='flex items-center gap-1'>
                  <Label className='text-xs text-gray-600 whitespace-nowrap w-16'>现场调度：</Label>
                  <EditableComboBox
                    value={formData.siteDispatcher}
                    onValueChange={v => updateField('siteDispatcher', v)}
                    options={fieldOptions.siteDispatcher}
                    onAddOption={option => addOption('siteDispatcher', option)}
                    onRemoveOption={option => removeOption('siteDispatcher', option)}
                    className='h-7 text-xs'
                    placeholder='请选择或输入现场调度'
                    allowAdd={true}
                    allowRemove={true}
                    addFromInput={true}
                  />
                </div>
                <div className='flex items-center gap-1'>
                  <div className='flex items-center gap-1'>
                    <Label className='text-xs text-gray-600 whitespace-nowrap w-16'>
                      预存余额：
                    </Label>
                    <Input
                      value={formData.prepaidBalance}
                      className='h-7 text-xs bg-slate-50 border-slate-200 text-slate-700 flex-1'
                      readOnly
                    />
                  </div>
                  <div className='flex items-center gap-1'>
                    <Label className='text-xs text-gray-600 whitespace-nowrap w-16'>调度员：</Label>
                    <Input
                      value={formData.dispatcher}
                      className='h-7 text-xs bg-slate-50 border-slate-200 text-slate-700 flex-1'
                      readOnly
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* 第七行：方量设定 */}
            <div className='bg-white rounded-lg border border-slate-200 shadow-sm p-1'>
              <div className='flex items-center gap-1'>
                <Label className='text-xs text-gray-600 whitespace-nowrap'>方量设定：</Label>
                <div className='flex items-center gap-1'>
                  <Checkbox
                    checked={formData.maxSetting}
                    onCheckedChange={checked => updateField('maxSetting', checked)}
                    className='h-4 w-4'
                  />
                  <span className='text-xs text-gray-600'>最大设定</span>
                </div>
                <div className='flex items-center gap-1'>
                  <Checkbox
                    checked={formData.noOverload}
                    onCheckedChange={checked => updateField('noOverload', checked)}
                    className='h-4 w-4'
                  />
                  <span className='text-xs text-gray-600'>不超载</span>
                </div>
                <div className='flex items-center gap-1'>
                  <Checkbox
                    checked={formData.adjustByWeight}
                    onCheckedChange={checked => updateField('adjustByWeight', checked)}
                    className='h-4 w-4'
                  />
                  <span className='text-xs text-gray-600'>根据皮重实时调整</span>
                </div>
                <Label className='text-xs text-gray-600 whitespace-nowrap ml-4'>方量：</Label>
                <Input
                  type='number'
                  value={formData.volumeFrom}
                  onChange={e => updateField('volumeFrom', Number(e.target.value))}
                  className='h-7 text-xs border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100 transition-colors w-16'
                  placeholder='12'
                />
                <span className='text-xs text-gray-600'>-</span>
                <Input
                  type='number'
                  value={formData.volumeTo}
                  onChange={e => updateField('volumeTo', Number(e.target.value))}
                  className='h-7 text-xs border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100 transition-colors w-16'
                  placeholder='12'
                />
                <Label className='text-xs text-gray-600 whitespace-nowrap ml-4'>锁：</Label>
                <Checkbox
                  checked={formData.volumeLock}
                  onCheckedChange={checked => updateField('volumeLock', checked)}
                  className='h-4 w-4'
                />
                <Label className='text-xs text-gray-600 whitespace-nowrap ml-4'>量：</Label>
                <Input
                  type='number'
                  value={formData.volumeQuantity}
                  onChange={e => updateField('volumeQuantity', Number(e.target.value))}
                  className='h-7 text-xs border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100 transition-colors w-16'
                  placeholder='1'
                />
              </div>
            </div>

            {/* 第八行：打印设置 */}
            <div className='bg-white rounded-lg border border-slate-200 shadow-sm p-1'>
              <div className='grid grid-cols-3 gap-3'>
                <div className='flex items-center gap-1'>
                  <Label className='text-xs text-gray-600 whitespace-nowrap w-16'>打印方量：</Label>
                  <Input
                    type='number'
                    value={formData.printVolume}
                    onChange={e => updateField('printVolume', Number(e.target.value))}
                    className='h-7 text-xs border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100 transition-colors flex-1'
                    placeholder='6.57'
                  />
                </div>
                <div className='flex items-center gap-1'>
                  <Label className='text-xs text-gray-600 whitespace-nowrap w-16'>打印重量：</Label>
                  <Input
                    type='number'
                    value={formData.printWeight}
                    onChange={e => updateField('printWeight', Number(e.target.value))}
                    className='h-7 text-xs border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100 transition-colors flex-1'
                    placeholder='130.04'
                  />
                </div>
                <div className='flex items-center gap-1'>
                  <Label className='text-xs text-gray-600 whitespace-nowrap w-16'>发车方式：</Label>
                  <Select
                    value={formData.dispatchMethod}
                    onValueChange={v => updateField('dispatchMethod', v)}
                  >
                    <SelectTrigger className='h-7 text-xs border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100 flex-1'>
                      <SelectValue placeholder='生成发货单' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='生成发货单'>生成发货单</SelectItem>
                      <SelectItem value='直接发车'>直接发车</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* 主要数据表格区域 */}
            <div className='bg-white rounded-lg border border-slate-200 shadow-sm p-1 pb-2'>
              <div className='grid grid-cols-2 gap-4'>
                {/* 左侧：车辆信息表格 */}
                <div>
                  <div className='border border-slate-200 rounded overflow-hidden'>
                    {/* 表头 */}
                    <div className='bg-slate-50 border-b border-slate-200'>
                      <table className='w-full text-xs table-fixed'>
                        <colgroup>
                          <col style={{ width: '16%' }} />
                          <col style={{ width: '16%' }} />
                          <col style={{ width: '16%' }} />
                          <col style={{ width: '16%' }} />
                          <col style={{ width: '16%' }} />
                          <col style={{ width: '16%' }} />
                        </colgroup>
                        <thead>
                          <tr>
                            <th className='border-r border-slate-200 p-1 text-center text-xs font-medium'>
                              车号
                            </th>
                            <th className='border-r border-slate-200 p-1 text-center text-xs font-medium'>
                              司机
                            </th>
                            <th className='border-r border-slate-200 p-1 text-center text-xs font-medium'>
                              方量
                            </th>
                            <th className='border-r border-slate-200 p-1 text-center text-xs font-medium'>
                              超量
                            </th>
                            <th className='border-r border-slate-200 p-1 text-center text-xs font-medium'>
                              超重
                            </th>
                            <th className='p-1 text-center text-xs font-medium'>操作</th>
                          </tr>
                        </thead>
                      </table>
                    </div>

                    {/* 表体 - 固定4行高度，超过滚动 */}
                    <div className='h-24 overflow-y-auto'>
                      <table className='w-full text-xs table-fixed'>
                        <colgroup>
                          <col style={{ width: '16%' }} />
                          <col style={{ width: '16%' }} />
                          <col style={{ width: '16%' }} />
                          <col style={{ width: '16%' }} />
                          <col style={{ width: '16%' }} />
                          <col style={{ width: '14%' }} />
                        </colgroup>
                        <tbody>
                          {/* 实际数据行 */}
                          {formData.selectedVehicles.map((vehicle, index) => (
                            <tr
                              key={index}
                              className='h-6 border-t border-slate-200 first:border-t-0 hover:bg-blue-50'
                            >
                              <td
                                className='border-r border-slate-200 cursor-pointer text-blue-600 hover:text-blue-800 hover:bg-blue-100 transition-colors relative group'
                                onClick={() => handleVehicleEdit(index)}
                                title='点击选择车辆'
                              >
                                <div className='flex items-center justify-between'>
                                  <span className='flex-1 text-center'>
                                    {vehicle.vehicleNumber}
                                  </span>
                                  <div className='flex items-center gap-0.5'>
                                    <div className='w-4 h-4 bg-blue-500 hover:bg-blue-600 rounded-sm flex items-center justify-center transition-colors'>
                                      <svg
                                        className='w-2.5 h-2.5 text-white'
                                        fill='none'
                                        stroke='currentColor'
                                        viewBox='0 0 24 24'
                                      >
                                        <path
                                          strokeLinecap='round'
                                          strokeLinejoin='round'
                                          strokeWidth={3}
                                          d='M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z'
                                        />
                                      </svg>
                                    </div>
                                  </div>
                                </div>
                              </td>
                              <td className='border-r border-slate-200 text-center'>
                                {vehicle.driverName}
                              </td>
                              <td className='border-r border-slate-200 text-center'>
                                {vehicle.volume}
                              </td>
                              <td className='border-r border-slate-200 text-center'>
                                {vehicle.overVolume}
                              </td>
                              <td className='border-r border-slate-200 text-center'>
                                {vehicle.overWeight}
                              </td>
                              <td className='text-center'>
                                <Button
                                  variant='ghost'
                                  size='sm'
                                  onClick={() => handleVehicleRemove(index)}
                                  className='h-5 w-12 px-1 text-xs text-red-500 hover:text-red-700 hover:bg-red-50 border border-red-200 hover:border-red-300 transition-colors'
                                  title='清空此行'
                                >
                                  清空
                                </Button>
                              </td>
                            </tr>
                          ))}

                          {/* 动态填充空行 */}
                          {Array.from({
                            length:
                              formData.selectedVehicles.length <= 4
                                ? 4 - formData.selectedVehicles.length + 1 // 少于等于4行时，填充到5行（至少1行空行）
                                : 1, // 大于4行时，只填充1行空行
                          }).map((_, index) => (
                            <tr
                              key={`empty-${index}`}
                              className='h-6 border-t border-slate-200 hover:bg-gray-50'
                            >
                              <td
                                className='border-r border-slate-200 cursor-pointer text-gray-400 hover:text-blue-600 hover:bg-blue-50 transition-colors'
                                onClick={() => {
                                  setEditingVehicleIndex(null);
                                  setIsVehicleSelectOpen(true);
                                }}
                                title='点击添加车辆'
                              >
                                <div className='flex items-center justify-between'>
                                  <span className='flex-1 text-center'>-</span>
                                  <div className='flex items-center gap-0.5'>
                                    <div className='w-4 h-4 bg-gray-400 hover:bg-blue-500 rounded-sm flex items-center justify-center transition-colors'>
                                      <svg
                                        className='w-2.5 h-2.5 text-white'
                                        fill='none'
                                        stroke='currentColor'
                                        viewBox='0 0 24 24'
                                      >
                                        <path
                                          strokeLinecap='round'
                                          strokeLinejoin='round'
                                          strokeWidth={3}
                                          d='M12 4v16m8-8H4'
                                        />
                                      </svg>
                                    </div>
                                  </div>
                                </div>
                              </td>
                              <td className='border-r border-slate-200  text-center text-gray-300'>
                                -
                              </td>
                              <td className='border-r border-slate-200 text-center text-gray-300'>
                                -
                              </td>
                              <td className='border-r border-slate-200  text-center text-gray-300'>
                                -
                              </td>
                              <td className='border-r border-slate-200  text-center text-gray-300'>
                                -
                              </td>
                              <td className='text-center'>
                                <Button
                                  variant='ghost'
                                  size='sm'
                                  disabled
                                  className='h-5 w-12 px-1 text-xs text-gray-300 border border-gray-200 cursor-not-allowed'
                                  title='无数据可清空'
                                >
                                  清空
                                </Button>
                              </td>
                            </tr>
                          ))}

                          {/* 无数据时的提示
                          {formData.selectedVehicles.length === 0 && (
                            <tr className='absolute inset-0 flex items-center justify-center'>
                              <td colSpan={6} className='text-center text-gray-500'>
                                <Button
                                  variant='outline'
                                  size='sm'
                                  onClick={() => {
                                    setEditingVehicleIndex(null);
                                    setIsVehicleSelectOpen(true);
                                  }}
                                  className='h-6 px-3 text-xs'
                                >
                                  <Plus className='w-3 h-3 mr-1' />
                                  添加车辆
                                </Button>
                              </td>
                            </tr>
                          )} */}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>

                {/* 右侧：其他设置 */}
                <div className='space-y-2'>
                  <div className='grid grid-cols-2 gap-2'>
                    <div className='flex items-center gap-1'>
                      <Label className='text-xs text-gray-600 whitespace-nowrap w-16'>
                        生产提示：
                      </Label>
                      <EditableComboBox
                        value={formData.productionTip}
                        onValueChange={v => updateField('productionTip', v)}
                        options={fieldOptions.productionTip}
                        onAddOption={option => addOption('productionTip', option)}
                        onRemoveOption={option => removeOption('productionTip', option)}
                        className='h-7 text-xs'
                        placeholder='请选择或输入生产提示'
                        allowAdd={true}
                        allowRemove={true}
                        addFromInput={true}
                      />
                    </div>
                    <div className='flex items-center gap-1'>
                      <Label className='text-xs text-gray-600 whitespace-nowrap w-16'>
                        司机提示：
                      </Label>
                      <EditableComboBox
                        value={formData.driverTip}
                        onValueChange={v => updateField('driverTip', v)}
                        options={fieldOptions.driverTip}
                        onAddOption={option => addOption('driverTip', option)}
                        onRemoveOption={option => removeOption('driverTip', option)}
                        className='h-7 text-xs'
                        placeholder='请选择或输入司机提示'
                        allowAdd={true}
                        allowRemove={true}
                        addFromInput={true}
                      />
                    </div>
                    <div className='flex items-center gap-1'>
                      <Label className='text-xs text-gray-600 whitespace-nowrap w-16'>
                        打印提示：
                      </Label>
                      <EditableComboBox
                        value={formData.printTip}
                        onValueChange={v => updateField('printTip', v)}
                        options={fieldOptions.printTip}
                        onAddOption={option => addOption('printTip', option)}
                        onRemoveOption={option => removeOption('printTip', option)}
                        className='h-7 text-xs'
                        placeholder='请选择或输入打印提示'
                        allowAdd={true}
                        allowRemove={true}
                        addFromInput={true}
                      />
                    </div>
                    <div className='flex items-center gap-1'>
                      <Label className='text-xs text-gray-600 whitespace-nowrap w-16'>备注：</Label>
                      <EditableComboBox
                        value={formData.notes}
                        onValueChange={v => updateField('notes', v)}
                        options={fieldOptions.notes}
                        onAddOption={option => addOption('notes', option)}
                        onRemoveOption={option => removeOption('notes', option)}
                        className='h-7 text-xs'
                        placeholder='请选择或输入备注'
                        allowAdd={true}
                        allowRemove={true}
                        addFromInput={true}
                      />
                    </div>
                    <div className='flex items-center gap-1'>
                      <Label className='text-xs text-gray-600 whitespace-nowrap w-16'>
                        泵车号：
                      </Label>
                      <Select
                        value={formData.pumpTruckNumber || ''}
                        onValueChange={handlePumpTruckSelect}
                      >
                        <SelectTrigger className='h-7 text-xs border-slate-200 focus:border-blue-400 focus:ring-1 focus:ring-blue-100 flex-1'>
                          <SelectValue placeholder='请选择操作' />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value='安排泵车'>安排泵车</SelectItem>
                          <SelectItem value='返回泵车'>返回泵车</SelectItem>
                        </SelectContent>
                      </Select>
                      {formData.pumpTruckNumber && (
                        <div className='flex items-center gap-1 ml-2'>
                          <span className='text-xs text-blue-600 font-medium'>
                            {formData.pumpTruckNumber}
                          </span>
                          <Button
                            variant='ghost'
                            size='sm'
                            onClick={() => updateField('pumpTruckNumber', '')}
                            className='h-5 w-5 p-0 text-gray-400 hover:text-red-500'
                            title='清除泵车号'
                          >
                            ×
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 复选框选项 - 横向排列节省空间 */}
                  <div className='mt-2'>
                    <div className='flex flex-wrap items-center gap-4'>
                      <div className='flex items-center gap-1'>
                        <Checkbox
                          checked={formData.autoDispatch}
                          onCheckedChange={checked => updateField('autoDispatch', checked)}
                          className='h-4 w-4'
                        />
                        <span className='text-xs text-gray-600'>自动调度</span>
                      </div>
                      <div className='flex items-center gap-1'>
                        <Checkbox
                          checked={formData.backSandMortar}
                          onCheckedChange={checked => updateField('backSandMortar', checked)}
                          className='h-4 w-4'
                        />
                        <span className='text-xs text-gray-600'>背砂浆</span>
                      </div>
                      <div className='flex items-center gap-1'>
                        <Checkbox
                          checked={formData.washingWater}
                          onCheckedChange={checked => updateField('washingWater', checked)}
                          className='h-4 w-4'
                        />
                        <span className='text-xs text-gray-600'>接洗水</span>
                      </div>
                      <div className='flex items-center gap-1'>
                        <Checkbox
                          checked={formData.withPump}
                          onCheckedChange={checked => updateField('withPump', checked)}
                          className='h-4 w-4'
                        />
                        <span className='text-xs text-gray-600'>带泵车</span>
                      </div>
                      <div className='flex items-center gap-1'>
                        <Checkbox
                          checked={formData.noMixingStation}
                          onCheckedChange={checked => updateField('noMixingStation', checked)}
                          className='h-4 w-4'
                        />
                        <span className='text-xs text-gray-600'>不发往搅拌站</span>
                      </div>
                      <div className='flex items-center gap-1'>
                        <Checkbox
                          checked={formData.allowHouseRepair}
                          onCheckedChange={checked => updateField('allowHouseRepair', checked)}
                          className='h-4 w-4'
                        />
                        <span className='text-xs text-gray-600'>允许房修方量</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 底部按钮区域 */}
          <div className='flex items-center justify-between px-4 py-2 bg-slate-50 border-t border-slate-200'>
            <div className='flex items-center gap-2'>
              <div className='flex items-center gap-1'>
                <Checkbox
                  checked={formData.printTicket}
                  onCheckedChange={checked => updateField('printTicket', checked)}
                  className='h-4 w-4'
                />
                <span className='text-xs text-gray-600'>打票</span>
              </div>
              <Input
                value={formData.ticketNote}
                onChange={e => updateField('ticketNote', e.target.value)}
                className='h-7 text-xs border-slate-200 w-32'
                placeholder='备注信息'
              />
            </div>

            <div className='flex items-center gap-2'>
              <Button
                variant='outline'
                size='sm'
                className='h-8 px-4 text-xs'
                onClick={() => onOpenChange(false)}
              >
                取消
              </Button>
              <Button
                variant='default'
                size='sm'
                className='h-8 px-4 text-xs bg-blue-600 hover:bg-blue-700'
                onClick={handleDispatch}
              >
                <Save className='w-3 h-3 mr-1' />
                保存
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* 车辆选择模态框 */}
      <Dialog open={isVehicleSelectOpen} onOpenChange={setIsVehicleSelectOpen}>
        <DialogContent className='max-w-md'>
          <DialogHeader>
            <DialogTitle>选择车辆</DialogTitle>
          </DialogHeader>
          <div className='space-y-2 max-h-60 overflow-y-auto'>
            {availableVehicles.map(vehicle => (
              <div
                key={vehicle.vehicleNumber}
                className='flex items-center justify-between p-2 border rounded hover:bg-slate-50 cursor-pointer'
                onClick={() => handleVehicleSelect(vehicle)}
              >
                <div className='flex items-center gap-2'>
                  <Truck className='w-4 h-4 text-blue-600' />
                  <div>
                    <div className='text-sm font-medium'>{vehicle.vehicleNumber}</div>
                    <div className='text-xs text-gray-500'>{vehicle.driverName}</div>
                  </div>
                </div>
                <div className='text-xs text-gray-500'>{vehicle.capacity}方</div>
              </div>
            ))}
          </div>
        </DialogContent>
      </Dialog>

      {/* 泵车发车单模态框 */}
      <PumpTruckDispatchModal
        open={isPumpTruckDispatchOpen}
        onOpenChange={setIsPumpTruckDispatchOpen}
        onConfirm={pumpTruckData => {
          // 从泵车发车单数据中提取泵车号
          const pumpTruckNumber =
            pumpTruckData.pumpTruckNumber || `泵车${Date.now().toString().slice(-3)}`;
          handlePumpTruckDispatchComplete(pumpTruckNumber);
        }}
      />

      {/* 返回泵车列表模态框 */}
      <Dialog open={isPumpTruckReturnOpen} onOpenChange={setIsPumpTruckReturnOpen}>
        <DialogContent className='max-w-md'>
          <DialogHeader>
            <DialogTitle>选择返回泵车</DialogTitle>
          </DialogHeader>
          <div className='space-y-2 max-h-60 overflow-y-auto'>
            {['泵车001', '泵车002', '泵车003', '泵车004'].map(pumpTruck => (
              <div
                key={pumpTruck}
                className='flex items-center justify-between p-3 border rounded hover:bg-slate-50 cursor-pointer'
                onClick={() => handlePumpTruckReturnComplete(pumpTruck)}
              >
                <div className='flex items-center gap-2'>
                  <Truck className='w-4 h-4 text-green-600' />
                  <div>
                    <div className='text-sm font-medium'>{pumpTruck}</div>
                    <div className='text-xs text-gray-500'>可返回</div>
                  </div>
                </div>
                <div className='text-xs text-green-600'>点击返回</div>
              </div>
            ))}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};
