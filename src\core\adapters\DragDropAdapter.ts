/**
 * 拖拽库适配器 - 统一拖拽接口
 * 支持 @dnd-kit 和 react-dnd 的可插拔切换
 */

import React from 'react';

export type DragDropLibrary = 'dnd-kit' | 'react-dnd';

// 统一的拖拽数据接口
export interface UnifiedDragItem {
  id: string;
  type: string;
  data: Record<string, any>;
}

// 统一的拖拽状态接口
export interface UnifiedDragState {
  isDragging: boolean;
  draggedItem: UnifiedDragItem | null;
  isOver: boolean;
  canDrop: boolean;
}

// 统一的拖拽配置接口
export interface UnifiedDragConfig {
  id: string;
  type: string;
  data?: Record<string, any>;
  disabled?: boolean;
}

export interface UnifiedDropConfig {
  accept: string | string[];
  onDrop?: (item: UnifiedDragItem) => void;
  canDrop?: (item: UnifiedDragItem) => boolean;
  disabled?: boolean;
}

// 统一的拖拽Hook返回值
export interface UnifiedDragResult {
  ref: React.MutableRefObject<HTMLElement | null>;
  dragRef?: (node: HTMLElement | null) => void; // 回调 ref，用于 React DnD
  isDragging: boolean;
  attributes?: Record<string, any>;
  listeners?: Record<string, any>;
}

export interface UnifiedDropResult {
  ref: React.RefObject<HTMLElement>;
  dropRef?: (node: HTMLElement | null) => void; // 回调 ref，用于 React DnD
  isOver: boolean;
  canDrop: boolean;
}

/**
 * 拖拽适配器接口
 */
export interface IDragDropAdapter {
  readonly library: DragDropLibrary;

  // 拖拽源适配
  useDrag(config: UnifiedDragConfig): UnifiedDragResult;

  // 拖拽目标适配
  useDrop(config: UnifiedDropConfig): UnifiedDropResult;

  // 类型转换
  convertItemType(type: string): string;

  // 数据转换
  convertDragData(data: Record<string, any>): any;
}

/**
 * 拖拽Provider工厂接口
 */
export interface IDragDropProviderFactory {
  createProvider(): React.ComponentType<{ children: React.ReactNode }>;
}

/**
 * 适配器工厂
 */
export class DragDropAdapterFactory {
  private static adapters: Map<DragDropLibrary, IDragDropAdapter> = new Map();
  private static providers: Map<DragDropLibrary, IDragDropProviderFactory> = new Map();

  static register(library: DragDropLibrary, adapter: IDragDropAdapter, providerFactory?: IDragDropProviderFactory) {
    this.adapters.set(library, adapter);
    if (providerFactory) {
      this.providers.set(library, providerFactory);
    }
  }

  static create(library: DragDropLibrary): IDragDropAdapter {
    const adapter = this.adapters.get(library);
    if (!adapter) {
      throw new Error(`Drag drop adapter for ${library} not found`);
    }
    return adapter;
  }

  static createProvider(library: DragDropLibrary): React.ComponentType<{ children: React.ReactNode }> | null {
    const providerFactory = this.providers.get(library);
    return providerFactory ? providerFactory.createProvider() : null;
  }

  static getAvailable(): DragDropLibrary[] {
    return Array.from(this.adapters.keys());
  }
}

/**
 * 统一拖拽Hook - 自动选择适配器
 */
export function useUnifiedDrag(config: UnifiedDragConfig): UnifiedDragResult {
  // 根据配置或环境变量选择适配器
  const library = (process.env['NEXT_PUBLIC_DRAG_LIBRARY'] as DragDropLibrary) || 'react-dnd';
  const adapter = DragDropAdapterFactory.create(library);
  return adapter.useDrag(config);
}

export function useUnifiedDrop(config: UnifiedDropConfig): UnifiedDropResult {
  const library = (process.env['NEXT_PUBLIC_DRAG_LIBRARY'] as DragDropLibrary) || 'react-dnd';
  const adapter = DragDropAdapterFactory.create(library);
  return adapter.useDrop(config);
}

/**
 * 配置管理
 */
export interface DragDropConfig {
  defaultLibrary: DragDropLibrary;
  featureMapping: Record<string, DragDropLibrary>;
  migrationMode: boolean;
}

export const dragDropConfig: DragDropConfig = {
  defaultLibrary: 'react-dnd',
  featureMapping: {
    'task-list': 'react-dnd',
    'vehicle-dispatch': 'react-dnd', 
    'ratio-design': 'dnd-kit',
    'card-config': 'dnd-kit',
  },
  migrationMode: true, // 启用迁移模式，支持两个库并存
};
