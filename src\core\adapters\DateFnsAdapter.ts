/**
 * Date-fns 日期适配器实现
 * 将 date-fns 库适配到统一的日期接口
 */

import {
  format,
  formatDistance,
  formatRelative,
  add,
  sub,
  differenceInMilliseconds,
  differenceInSeconds,
  differenceInMinutes,
  differenceInHours,
  differenceInDays,
  differenceInMonths,
  differenceInYears,
  isSameDay,
  isSameMonth,
  isSameYear,
  isSameHour,
  isSameMinute,
  isSameSecond,
  isBefore,
  isAfter,
  isValid,
  parse,
  parseISO,
  startOfDay,
  endOfDay,
  startOfMonth,
  endOfMonth,
  startOfYear,
  endOfYear,
  startOfHour,
  endOfHour,
  startOfMinute,
  endOfMinute,
  startOfSecond,
  endOfSecond,
  getYear,
  getMonth,
  getDate,
  getHours,
  getMinutes,
  getSeconds,
  setYear,
  setMonth,
  setDate,
  setHours,
  setMinutes,
  setSeconds,
} from 'date-fns';
import { zhCN } from 'date-fns/locale';

import { DateAdapter, DateLibrary, DateUnit, UnifiedDate } from './DateAdapter';

/**
 * 将 dayjs 格式转换为 date-fns 格式
 * dayjs 和 date-fns 在某些格式标记上有差异
 */
function convertFormatPattern(pattern: string): string {
  return pattern
    .replace(/YYYY/g, 'yyyy')  // 年份：YYYY -> yyyy
    .replace(/YY/g, 'yy')      // 年份简写：YY -> yy
    .replace(/DD/g, 'dd')      // 日期：DD -> dd
    .replace(/D/g, 'd')        // 日期简写：D -> d
    .replace(/HH/g, 'HH')      // 小时：HH -> HH (24小时制，保持不变)
    .replace(/H/g, 'H')        // 小时简写：H -> H (保持不变)
    .replace(/hh/g, 'hh')      // 小时：hh -> hh (12小时制，保持不变)
    .replace(/h/g, 'h')        // 小时简写：h -> h (保持不变)
    .replace(/mm/g, 'mm')      // 分钟：mm -> mm (保持不变)
    .replace(/m/g, 'm')        // 分钟简写：m -> m (保持不变)
    .replace(/ss/g, 'ss')      // 秒：ss -> ss (保持不变)
    .replace(/s/g, 's')        // 秒简写：s -> s (保持不变)
    .replace(/MM/g, 'MM')      // 月份：MM -> MM (保持不变)
    .replace(/M/g, 'M')        // 月份简写：M -> M (保持不变)
    .replace(/A/g, 'a')        // AM/PM：A -> a
    .replace(/a/g, 'a');       // am/pm：a -> a (保持不变)
}

/**
 * Date-fns 统一日期对象实现
 */
class DateFnsUnifiedDate implements UnifiedDate {
  constructor(private dateInstance: Date) {}

  get value(): Date {
    return this.dateInstance;
  }

  format(pattern: string): string {
    const convertedPattern = convertFormatPattern(pattern);
    return format(this.dateInstance, convertedPattern, { locale: zhCN });
  }

  add(amount: number, unit: DateUnit): UnifiedDate {
    const duration = { [unit + 's']: amount };
    return new DateFnsUnifiedDate(add(this.dateInstance, duration));
  }

  subtract(amount: number, unit: DateUnit): UnifiedDate {
    const duration = { [unit + 's']: amount };
    return new DateFnsUnifiedDate(sub(this.dateInstance, duration));
  }

  isSame(date: UnifiedDate | Date | string, unit?: DateUnit): boolean {
    const targetDate = this.convertToDate(date);
    
    switch (unit) {
      case 'year':
        return isSameYear(this.dateInstance, targetDate);
      case 'month':
        return isSameMonth(this.dateInstance, targetDate);
      case 'day':
        return isSameDay(this.dateInstance, targetDate);
      case 'hour':
        return isSameHour(this.dateInstance, targetDate);
      case 'minute':
        return isSameMinute(this.dateInstance, targetDate);
      case 'second':
        return isSameSecond(this.dateInstance, targetDate);
      default:
        return this.dateInstance.getTime() === targetDate.getTime();
    }
  }

  isBefore(date: UnifiedDate | Date | string): boolean {
    const targetDate = this.convertToDate(date);
    return isBefore(this.dateInstance, targetDate);
  }

  isAfter(date: UnifiedDate | Date | string): boolean {
    const targetDate = this.convertToDate(date);
    return isAfter(this.dateInstance, targetDate);
  }

  isValid(): boolean {
    return isValid(this.dateInstance);
  }

  toDate(): Date {
    return this.dateInstance;
  }

  toISOString(): string {
    return this.dateInstance.toISOString();
  }

  valueOf(): number {
    return this.dateInstance.valueOf();
  }

  private convertToDate(date: UnifiedDate | Date | string): Date {
    if (date instanceof Date) {
      return date;
    }
    if (typeof date === 'string') {
      return new Date(date);
    }
    return date.toDate();
  }
}

/**
 * Date-fns 适配器实现
 */
export class DateFnsAdapter extends DateAdapter {
  readonly library: DateLibrary = 'date-fns';

  create(input?: string | Date | number): UnifiedDate {
    let date: Date;
    if (input === undefined) {
      date = new Date();
    } else if (typeof input === 'string') {
      date = new Date(input);
    } else if (typeof input === 'number') {
      date = new Date(input);
    } else {
      date = input;
    }
    return new DateFnsUnifiedDate(date);
  }

  now(): UnifiedDate {
    return new DateFnsUnifiedDate(new Date());
  }

  today(): UnifiedDate {
    return new DateFnsUnifiedDate(startOfDay(new Date()));
  }

  format(date: Date | string, pattern: string): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const convertedPattern = convertFormatPattern(pattern);
    return format(dateObj, convertedPattern, { locale: zhCN });
  }

  formatDistance(date: Date | string, baseDate?: Date | string): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const baseObj = baseDate ? (typeof baseDate === 'string' ? new Date(baseDate) : baseDate) : new Date();
    return formatDistance(dateObj, baseObj, { locale: zhCN });
  }

  formatRelative(date: Date | string, baseDate?: Date | string): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const baseObj = baseDate ? (typeof baseDate === 'string' ? new Date(baseDate) : baseDate) : new Date();
    return formatRelative(dateObj, baseObj, { locale: zhCN });
  }

  add(date: Date | string, amount: number, unit: DateUnit): UnifiedDate {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const duration = { [unit + 's']: amount };
    return new DateFnsUnifiedDate(add(dateObj, duration));
  }

  subtract(date: Date | string, amount: number, unit: DateUnit): UnifiedDate {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const duration = { [unit + 's']: amount };
    return new DateFnsUnifiedDate(sub(dateObj, duration));
  }

  diff(dateLeft: Date | string, dateRight: Date | string, unit?: DateUnit): number {
    const leftObj = typeof dateLeft === 'string' ? new Date(dateLeft) : dateLeft;
    const rightObj = typeof dateRight === 'string' ? new Date(dateRight) : dateRight;
    
    switch (unit) {
      case 'year':
        return differenceInYears(leftObj, rightObj);
      case 'month':
        return differenceInMonths(leftObj, rightObj);
      case 'day':
        return differenceInDays(leftObj, rightObj);
      case 'hour':
        return differenceInHours(leftObj, rightObj);
      case 'minute':
        return differenceInMinutes(leftObj, rightObj);
      case 'second':
        return differenceInSeconds(leftObj, rightObj);
      case 'millisecond':
        return differenceInMilliseconds(leftObj, rightObj);
      default:
        return differenceInMilliseconds(leftObj, rightObj);
    }
  }

  isSame(dateLeft: Date | string, dateRight: Date | string, unit?: DateUnit): boolean {
    const leftObj = typeof dateLeft === 'string' ? new Date(dateLeft) : dateLeft;
    const rightObj = typeof dateRight === 'string' ? new Date(dateRight) : dateRight;
    
    switch (unit) {
      case 'year':
        return isSameYear(leftObj, rightObj);
      case 'month':
        return isSameMonth(leftObj, rightObj);
      case 'day':
        return isSameDay(leftObj, rightObj);
      case 'hour':
        return isSameHour(leftObj, rightObj);
      case 'minute':
        return isSameMinute(leftObj, rightObj);
      case 'second':
        return isSameSecond(leftObj, rightObj);
      default:
        return leftObj.getTime() === rightObj.getTime();
    }
  }

  isBefore(dateLeft: Date | string, dateRight: Date | string): boolean {
    const leftObj = typeof dateLeft === 'string' ? new Date(dateLeft) : dateLeft;
    const rightObj = typeof dateRight === 'string' ? new Date(dateRight) : dateRight;
    return isBefore(leftObj, rightObj);
  }

  isAfter(dateLeft: Date | string, dateRight: Date | string): boolean {
    const leftObj = typeof dateLeft === 'string' ? new Date(dateLeft) : dateLeft;
    const rightObj = typeof dateRight === 'string' ? new Date(dateRight) : dateRight;
    return isAfter(leftObj, rightObj);
  }

  isValid(date: any): boolean {
    if (date instanceof Date) {
      return isValid(date);
    }
    if (typeof date === 'string' || typeof date === 'number') {
      return isValid(new Date(date));
    }
    return false;
  }

  parse(dateString: string, formatString: string): UnifiedDate {
    const parsed = parse(dateString, formatString, new Date(), { locale: zhCN });
    return new DateFnsUnifiedDate(parsed);
  }

  parseISO(dateString: string): UnifiedDate {
    return new DateFnsUnifiedDate(parseISO(dateString));
  }

  startOf(date: Date | string, unit: DateUnit): UnifiedDate {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    
    switch (unit) {
      case 'year':
        return new DateFnsUnifiedDate(startOfYear(dateObj));
      case 'month':
        return new DateFnsUnifiedDate(startOfMonth(dateObj));
      case 'day':
        return new DateFnsUnifiedDate(startOfDay(dateObj));
      case 'hour':
        return new DateFnsUnifiedDate(startOfHour(dateObj));
      case 'minute':
        return new DateFnsUnifiedDate(startOfMinute(dateObj));
      case 'second':
        return new DateFnsUnifiedDate(startOfSecond(dateObj));
      default:
        return new DateFnsUnifiedDate(dateObj);
    }
  }

  endOf(date: Date | string, unit: DateUnit): UnifiedDate {
    const dateObj = typeof date === 'string' ? new Date(date) : date;

    switch (unit) {
      case 'year':
        return new DateFnsUnifiedDate(endOfYear(dateObj));
      case 'month':
        return new DateFnsUnifiedDate(endOfMonth(dateObj));
      case 'day':
        return new DateFnsUnifiedDate(endOfDay(dateObj));
      case 'hour':
        return new DateFnsUnifiedDate(endOfHour(dateObj));
      case 'minute':
        return new DateFnsUnifiedDate(endOfMinute(dateObj));
      case 'second':
        return new DateFnsUnifiedDate(endOfSecond(dateObj));
      default:
        return new DateFnsUnifiedDate(dateObj);
    }
  }

  getYear(date: Date | string): number {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return getYear(dateObj);
  }

  getMonth(date: Date | string): number {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return getMonth(dateObj);
  }

  getDate(date: Date | string): number {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return getDate(dateObj);
  }

  getHour(date: Date | string): number {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return getHours(dateObj);
  }

  getMinute(date: Date | string): number {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return getMinutes(dateObj);
  }

  getSecond(date: Date | string): number {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return getSeconds(dateObj);
  }

  setYear(date: Date | string, year: number): UnifiedDate {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new DateFnsUnifiedDate(setYear(dateObj, year));
  }

  setMonth(date: Date | string, month: number): UnifiedDate {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new DateFnsUnifiedDate(setMonth(dateObj, month));
  }

  setDate(date: Date | string, day: number): UnifiedDate {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new DateFnsUnifiedDate(setDate(dateObj, day));
  }

  setHour(date: Date | string, hour: number): UnifiedDate {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new DateFnsUnifiedDate(setHours(dateObj, hour));
  }

  setMinute(date: Date | string, minute: number): UnifiedDate {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new DateFnsUnifiedDate(setMinutes(dateObj, minute));
  }

  setSecond(date: Date | string, second: number): UnifiedDate {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new DateFnsUnifiedDate(setSeconds(dateObj, second));
  }
}
