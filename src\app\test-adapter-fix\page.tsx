'use client';

import React, { useEffect, useState } from 'react';
import { useUnifiedDate } from '@/core/adapters/hooks/useUnifiedDate';
import { AdapterManager, AdapterType } from '@/core/adapters/AdapterManager';

export default function TestAdapterFixPage() {
  const [status, setStatus] = useState<string>('初始化中...');
  const [testResults, setTestResults] = useState<string[]>([]);

  // 测试不同功能的适配器
  const datetimePickerAdapter = useUnifiedDate(undefined, 'datetime-picker');
  const vehicleDispatchAdapter = useUnifiedDate(undefined, 'vehicle-dispatch');
  const countdownAdapter = useUnifiedDate(undefined, 'countdown');
  const taskSchedulingAdapter = useUnifiedDate(undefined, 'task-scheduling');

  useEffect(() => {
    const runTests = async () => {
      const results: string[] = [];
      
      try {
        // 测试适配器管理器
        const adapterManager = AdapterManager.getInstance();
        const metrics = adapterManager.getPerformanceMetrics();
        results.push(`✅ 适配器管理器正常，已注册 ${metrics.registeredCount} 个适配器`);
        
        // 测试获取适配器
        try {
          const dateAdapter = adapterManager.getAdapter(AdapterType.DATE, 'datetime-picker');
          results.push(`✅ datetime-picker 适配器获取成功: ${dateAdapter.library}`);
        } catch (error) {
          results.push(`❌ datetime-picker 适配器获取失败: ${error}`);
        }

        try {
          const vehicleAdapter = adapterManager.getAdapter(AdapterType.DATE, 'vehicle-dispatch');
          results.push(`✅ vehicle-dispatch 适配器获取成功: ${vehicleAdapter.library}`);
        } catch (error) {
          results.push(`❌ vehicle-dispatch 适配器获取失败: ${error}`);
        }

        // 测试日期操作
        try {
          const now = datetimePickerAdapter.now();
          const formatted = datetimePickerAdapter.format(now.value, 'YYYY-MM-DD HH:mm:ss');
          results.push(`✅ datetime-picker 日期格式化成功: ${formatted}`);
        } catch (error) {
          results.push(`❌ datetime-picker 日期格式化失败: ${error}`);
        }

        try {
          const now = new Date();
          const nextTime = new Date(now.getTime() + 30 * 60 * 1000); // 30分钟后
          const diff = vehicleDispatchAdapter.diff(nextTime, now, 'second');
          results.push(`✅ vehicle-dispatch 时间差计算成功: ${diff} 秒`);
        } catch (error) {
          results.push(`❌ vehicle-dispatch 时间差计算失败: ${error}`);
        }

        try {
          const now = new Date();
          const distance = countdownAdapter.formatDistance(now);
          results.push(`✅ countdown 相对时间格式化成功: ${distance}`);
        } catch (error) {
          results.push(`❌ countdown 相对时间格式化失败: ${error}`);
        }

        setStatus('✅ 测试完成');
        setTestResults(results);
        
      } catch (error) {
        setStatus(`❌ 测试失败: ${error}`);
        results.push(`❌ 总体测试失败: ${error}`);
        setTestResults(results);
      }
    };

    runTests();
  }, [datetimePickerAdapter, vehicleDispatchAdapter, countdownAdapter, taskSchedulingAdapter]);

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">🔧 适配器修复测试</h1>
        <p className="text-muted-foreground">
          测试适配器系统是否正确修复
        </p>
      </div>

      <div className="max-w-4xl mx-auto space-y-4">
        <div className="p-4 border rounded-lg">
          <h2 className="text-xl font-semibold mb-2">测试状态</h2>
          <p className="text-lg">{status}</p>
        </div>

        <div className="p-4 border rounded-lg">
          <h2 className="text-xl font-semibold mb-2">测试结果</h2>
          <div className="space-y-2">
            {testResults.map((result, index) => (
              <div 
                key={index} 
                className={`p-2 rounded ${
                  result.startsWith('✅') 
                    ? 'bg-green-50 text-green-800' 
                    : 'bg-red-50 text-red-800'
                }`}
              >
                {result}
              </div>
            ))}
          </div>
        </div>

        <div className="p-4 border rounded-lg">
          <h2 className="text-xl font-semibold mb-2">快速测试</h2>
          <div className="grid grid-cols-2 gap-4">
            <button 
              className="p-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              onClick={() => {
                try {
                  const now = datetimePickerAdapter.now();
                  alert(`当前时间: ${datetimePickerAdapter.format(now.value, 'YYYY-MM-DD HH:mm:ss')}`);
                } catch (error) {
                  alert(`错误: ${error}`);
                }
              }}
            >
              测试 DateTime Picker
            </button>
            
            <button 
              className="p-2 bg-green-500 text-white rounded hover:bg-green-600"
              onClick={() => {
                try {
                  const now = new Date();
                  const future = new Date(now.getTime() + 1800000); // 30分钟后
                  const diff = vehicleDispatchAdapter.diff(future, now, 'minute');
                  alert(`时间差: ${diff} 分钟`);
                } catch (error) {
                  alert(`错误: ${error}`);
                }
              }}
            >
              测试 Vehicle Dispatch
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
