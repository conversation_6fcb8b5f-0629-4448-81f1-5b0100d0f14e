// src/features/vehicle-dispatch/hooks/use-dispatched-vehicles-columns.tsx
import React, { useMemo } from 'react';
import { ColumnDef, CellContext } from '@tanstack/react-table';

import { useUnifiedDate } from '@/core/adapters/hooks/useUnifiedDate';

import { cn } from '@/core/lib/utils';
import type { DispatchedVehicle, DensityStyleValues } from '@/core/types';
import {
  DISPATCHED_VEHICLES_COLUMNS_CONFIG,
  type DispatchedVehicleColumnDefinition,
} from '../config/dispatched-vehicles-columns.config';

interface UseDispatchedVehiclesColumnsProps {
  columnOrder: string[];
  columnVisibility: Record<string, boolean>;
  columnWidths: Record<string, number>;
  densityStyles: DensityStyleValues;
}



/**
 * 格式化时长显示（分钟转换为小时分钟）
 */
const formatDuration = (minutes?: number): string => {
  if (!minutes || minutes <= 0) return '-';

  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;

  if (hours > 0) {
    return `${hours}h${remainingMinutes > 0 ? ` ${remainingMinutes}m` : ''}`;
  }
  return `${remainingMinutes}m`;
};

/**
 * 获取往返图标组件 - 单一状态版本
 */
const RoundTripIcon: React.FC<{ isRoundTrip: boolean }> = ({ isRoundTrip }) => {
  if (isRoundTrip) {
    return (
      <span className='text-red-600 text-xs' title='返程'>
        ←
      </span>
    );
  }
  return (
    <span className='text-blue-600 text-xs' title='往程'>
      →
    </span>
  );
};

/**
 * 已出厂车辆表格列Hook
 */
export function useDispatchedVehiclesColumns({
  columnOrder,
  columnVisibility,
  columnWidths,
  densityStyles,
}: UseDispatchedVehiclesColumnsProps) {
  // 使用日期适配器
  const dateAdapter = useUnifiedDate(undefined, 'vehicle-dispatch');

  // 格式化时间显示 - 使用适配器
  const formatTimeWithAdapter = (timeString?: string): string => {
    if (!timeString) return '-';
    try {
      return dateAdapter.format(timeString, 'HH:mm');
    } catch {
      return '-';
    }
  };

  // 生成表格列配置
  const tableColumns = useMemo<ColumnDef<DispatchedVehicle>[]>(() => {
    return Array.from(DISPATCHED_VEHICLES_COLUMNS_CONFIG).map(colDef => {
      const baseCol: ColumnDef<DispatchedVehicle> = {
        id: colDef.id,
        accessorKey: colDef.id,
        header: () => colDef.label,
        size: columnWidths[colDef.id] || colDef.defaultWidth || 100,
        minSize: colDef.minWidth || 50,
        maxSize: colDef.maxWidth,
        enableResizing: colDef.isResizable !== false,
        enableHiding: !colDef.isMandatory,
        meta: {
          customDef: {
            ...colDef,
            densityStyles: colDef.densityStyles || densityStyles,
          },
        },
        cell: (info: CellContext<DispatchedVehicle, any>) => {
          const vehicle = info.row.original;
          const columnId = info.column.id;

          // 基础样式类 - 简洁样式
          const baseClasses = 'truncate text-xs';

          switch (columnId) {
            case 'vehicleNumberWithStatus':
              return (
                <div className={cn(baseClasses, 'flex items-center gap-1 w-16')}>
                  <RoundTripIcon isRoundTrip={vehicle.isRoundTrip} />
                  <span
                    className='text-blue-600 font-medium truncate'
                    title={vehicle.vehicleNumber}
                  >
                    {vehicle.vehicleNumber}
                  </span>
                </div>
              );

            case 'driver':
              return (
                <div className={cn(baseClasses, 'w-16 truncate')} title={vehicle.driver}>
                  {vehicle.driver}
                </div>
              );

            case 'departureTime':
              return (
                <div
                  className={cn(baseClasses, 'w-16 truncate')}
                  title={formatTimeWithAdapter(vehicle.departureTime)}
                >
                  {formatTimeWithAdapter(vehicle.departureTime)}
                </div>
              );

            case 'arrivalTime':
              return (
                <div
                  className={cn(baseClasses, 'w-16 truncate')}
                  title={formatTimeWithAdapter(vehicle.arrivalTime)}
                >
                  {formatTimeWithAdapter(vehicle.arrivalTime)}
                </div>
              );

            case 'siteStayDuration':
              return (
                <div
                  className={cn(baseClasses, 'w-16 text-center truncate')}
                  title={formatDuration(vehicle.siteStayDuration)}
                >
                  {formatDuration(vehicle.siteStayDuration)}
                </div>
              );

            case 'returnTime':
              return (
                <div
                  className={cn(baseClasses, 'w-16 truncate')}
                  title={formatTimeWithAdapter(vehicle.returnTime)}
                >
                  {formatTimeWithAdapter(vehicle.returnTime)}
                </div>
              );

            case 'outboundDuration':
              return (
                <div
                  className={cn(baseClasses, 'w-16 text-center truncate')}
                  title={formatDuration(vehicle.outboundDuration)}
                >
                  {formatDuration(vehicle.outboundDuration)}
                </div>
              );

            case 'returnDuration':
              return (
                <div
                  className={cn(baseClasses, 'w-16 text-center truncate')}
                  title={formatDuration(vehicle.returnDuration)}
                >
                  {formatDuration(vehicle.returnDuration)}
                </div>
              );

            case 'totalDuration':
              return (
                <div
                  className={cn(baseClasses, 'w-16 text-center font-medium truncate')}
                  title={formatDuration(vehicle.totalDuration)}
                >
                  {formatDuration(vehicle.totalDuration)}
                </div>
              );

            case 'deliveryOrderNumber':
              return (
                <div
                  className={cn(baseClasses, 'w-16 font-mono truncate')}
                  title={vehicle.deliveryOrderNumber}
                >
                  {vehicle.deliveryOrderNumber}
                </div>
              );

            default:
              return <div className={baseClasses}>{String(info.getValue() || '-')}</div>;
          }
        },
      };

      return baseCol;
    });
  }, [columnWidths, densityStyles, dateAdapter]);

  // 根据列顺序和可见性过滤列
  const visibleColumns = useMemo(() => {
    return tableColumns
      .filter(col => columnVisibility[col.id!] !== false)
      .sort((a, b) => {
        const aIndex = columnOrder.indexOf(a.id!);
        const bIndex = columnOrder.indexOf(b.id!);
        return aIndex - bIndex;
      });
  }, [tableColumns, columnOrder, columnVisibility]);

  // 计算表格总宽度
  const tableTotalWidth = useMemo(() => {
    return visibleColumns.reduce((total, col) => {
      return total + (columnWidths[col.id!] || col.size || 100);
    }, 0);
  }, [visibleColumns, columnWidths]);

  return {
    tableColumns: visibleColumns,
    tableTotalWidth,
    allColumns: DISPATCHED_VEHICLES_COLUMNS_CONFIG,
  };
}
