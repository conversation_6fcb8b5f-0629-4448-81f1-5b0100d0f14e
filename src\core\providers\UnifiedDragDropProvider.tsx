/**
 * 统一拖拽Provider - 自动选择合适的拖拽库Provider
 */

'use client';

import React, { useMemo } from 'react';
import { AdapterManager, AdapterType } from '../adapters/AdapterManager';

interface UnifiedDragDropProviderProps {
  children: React.ReactNode;
  feature?: string; // 功能特性，用于选择合适的适配器
}

export function UnifiedDragDropProvider({ 
  children, 
  feature = 'task-list' 
}: UnifiedDragDropProviderProps) {
  const Provider = useMemo(() => {
    try {
      const adapterManager = AdapterManager.getInstance();
      return adapterManager.getProvider(AdapterType.DRAG_DROP, feature);
    } catch (error) {
      console.warn('⚠️ 获取拖拽Provider失败，使用默认Provider:', error);
      return null;
    }
  }, [feature]);

  if (!Provider) {
    // 如果没有找到Provider，直接返回children
    console.warn('⚠️ 未找到拖拽Provider，拖拽功能可能不可用');
    return <>{children}</>;
  }

  return <Provider>{children}</Provider>;
}

/**
 * 多功能拖拽Provider - 支持多个功能特性
 */
interface MultiFeatureDragDropProviderProps {
  children: React.ReactNode;
  features: string[]; // 支持的功能特性列表
}

export function MultiFeatureDragDropProvider({ 
  children, 
  features 
}: MultiFeatureDragDropProviderProps) {
  const providers = useMemo(() => {
    const adapterManager = AdapterManager.getInstance();
    const providerMap = new Map<string, React.ComponentType<{ children: React.ReactNode }>>();
    
    features.forEach(feature => {
      try {
        const provider = adapterManager.getProvider(AdapterType.DRAG_DROP, feature);
        if (provider) {
          providerMap.set(feature, provider);
        }
      } catch (error) {
        console.warn(`⚠️ 获取功能 ${feature} 的Provider失败:`, error);
      }
    });
    
    return Array.from(new Set(providerMap.values())); // 去重
  }, [features]);

  // 嵌套多个Provider
  return providers.reduce(
    (acc, Provider) => <Provider>{acc}</Provider>,
    <>{children}</>
  );
}

/**
 * 智能拖拽Provider - 根据环境自动选择
 */
export function SmartDragDropProvider({ children }: { children: React.ReactNode }) {
  const Provider = useMemo(() => {
    try {
      const adapterManager = AdapterManager.getInstance();
      
      // 根据当前页面或路由选择合适的Provider
      if (typeof window !== 'undefined') {
        const pathname = window.location.pathname;
        
        if (pathname.includes('/ratio')) {
          return adapterManager.getProvider(AdapterType.DRAG_DROP, 'ratio-design');
        } else if (pathname.includes('/task')) {
          return adapterManager.getProvider(AdapterType.DRAG_DROP, 'task-list');
        }
      }
      
      // 默认使用任务列表Provider
      return adapterManager.getProvider(AdapterType.DRAG_DROP, 'task-list');
    } catch (error) {
      console.warn('⚠️ 智能Provider选择失败:', error);
      return null;
    }
  }, []);

  if (!Provider) {
    return <>{children}</>;
  }

  return <Provider>{children}</Provider>;
}

/**
 * 开发环境拖拽调试Provider
 */
export function DebugDragDropProvider({ children }: { children: React.ReactNode }) {
  if (process.env.NODE_ENV !== 'development') {
    return <UnifiedDragDropProvider>{children}</UnifiedDragDropProvider>;
  }

  return (
    <UnifiedDragDropProvider>
      <div data-debug-drag-drop="true">
        {children}
        {/* 可以在这里添加调试UI */}
      </div>
    </UnifiedDragDropProvider>
  );
}

/**
 * 性能监控拖拽Provider
 */
export function PerformanceDragDropProvider({ children }: { children: React.ReactNode }) {
  const startTime = useMemo(() => Date.now(), []);

  React.useEffect(() => {
    const endTime = Date.now();
    const initTime = endTime - startTime;
    
    if (initTime > 100) {
      console.warn(`⚠️ 拖拽Provider初始化耗时过长: ${initTime}ms`);
    }
  }, [startTime]);

  return <UnifiedDragDropProvider>{children}</UnifiedDragDropProvider>;
}
