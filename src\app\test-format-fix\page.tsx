/**
 * 测试日期格式修复
 */

'use client';

import React, { useEffect, useState } from 'react';

export default function TestFormatFixPage() {
  const [results, setResults] = useState<string[]>([]);

  useEffect(() => {
    testDateFormats();
  }, []);

  const testDateFormats = async () => {
    const testResults: string[] = [];
    
    try {
      // 测试 DateFnsAdapter
      const { DateFnsAdapter } = await import('@/core/adapters/DateFnsAdapter');
      const adapter = new DateFnsAdapter();
      
      testResults.push('=== DateFnsAdapter 格式测试 ===');
      
      const testDate = new Date('2024-01-15 14:30:25');
      
      // 测试各种格式
      const formats = [
        'YYYY-MM-DD HH:mm:ss',
        'YYYY-MM-DD',
        'DD/MM/YYYY',
        'HH:mm:ss',
        'YYYY年MM月DD日',
        'MM-DD',
        'YY-MM-DD'
      ];
      
      for (const format of formats) {
        try {
          const result = adapter.format(testDate, format);
          testResults.push(`✅ ${format} -> ${result}`);
        } catch (error) {
          testResults.push(`❌ ${format} -> 错误: ${error}`);
        }
      }
      
      // 测试 UnifiedDate 对象的 format 方法
      testResults.push('\n=== UnifiedDate 格式测试 ===');
      const unifiedDate = adapter.create(testDate);
      
      for (const format of formats) {
        try {
          const result = unifiedDate.format(format);
          testResults.push(`✅ ${format} -> ${result}`);
        } catch (error) {
          testResults.push(`❌ ${format} -> 错误: ${error}`);
        }
      }
      
    } catch (error) {
      testResults.push(`❌ 测试失败: ${error}`);
    }
    
    setResults(testResults);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">🔧 日期格式修复测试</h1>
        <p className="text-muted-foreground">测试 DateFnsAdapter 格式转换修复</p>
      </div>

      <div className="bg-white border rounded-lg p-4">
        <h2 className="text-xl font-semibold mb-4">测试结果</h2>
        
        <div className="bg-gray-50 p-4 rounded max-h-96 overflow-y-auto">
          {results.map((result, index) => (
            <div key={index} className="text-sm font-mono mb-1 whitespace-pre-line">
              {result}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
