/**
 * 适配器注册中心 - 自动注册和初始化所有适配器
 */

import { AdapterManager, AdapterType, DragDropLibrary, DateLibrary } from './AdapterManager';
import { DragDropAdapterFactory } from './DragDropAdapter';
import { ReactDndAdapter, ReactDndProviderFactory } from './ReactDndAdapterV2';
import { DayjsAdapter } from './DayjsAdapter';
import { DateFnsAdapter } from './DateFnsAdapter';

/**
 * 适配器注册器
 */
export class AdapterRegistry {
  private static initialized = false;

  /**
   * 初始化所有适配器
   */
  static initialize(): void {
    if (this.initialized) {
      console.log('⚠️ 适配器已经初始化过了');
      return;
    }

    console.log('🚀 开始初始化适配器系统...');

    try {
      // 注册拖拽适配器
      this.registerDragDropAdapters();
      
      // 注册日期适配器
      this.registerDateAdapters();

      // 设置功能映射
      this.setupFeatureMappings();

      this.initialized = true;
      console.log('✅ 适配器系统初始化完成');
      
      // 输出性能指标
      const metrics = AdapterManager.getInstance().getPerformanceMetrics();
      console.log('📊 适配器性能指标:', metrics);
      
    } catch (error) {
      console.error('❌ 适配器系统初始化失败:', error);
      throw error;
    }
  }

  /**
   * 注册拖拽适配器
   */
  private static registerDragDropAdapters(): void {
    const adapterManager = AdapterManager.getInstance();

    // 注册 React DnD 适配器
    const reactDndAdapter = new ReactDndAdapter();
    const reactDndProviderFactory = new ReactDndProviderFactory();
    
    adapterManager.register({
      config: {
        type: AdapterType.DRAG_DROP,
        library: DragDropLibrary.REACT_DND,
        priority: 10,
        features: ['task-list', 'vehicle-dispatch', 'cross-task-transfer'],
        enabled: true,
      },
      factory: () => reactDndAdapter,
      provider: reactDndProviderFactory.createProvider(),
    });

    // 注册到旧的工厂系统（向后兼容）
    DragDropAdapterFactory.register(
      DragDropLibrary.REACT_DND, 
      reactDndAdapter, 
      reactDndProviderFactory
    );

    console.log('✅ React DnD 适配器已注册');

    // TODO: 注册 DnD Kit 适配器
    // const dndKitAdapter = new DndKitAdapter();
    // const dndKitProviderFactory = new DndKitProviderFactory();
    // adapterManager.register({...});
  }

  /**
   * 注册日期适配器
   */
  private static registerDateAdapters(): void {
    console.log('🔧 开始注册日期适配器...');
    const adapterManager = AdapterManager.getInstance();

    // 注册 Dayjs 适配器
    try {
      const dayjsAdapter = new DayjsAdapter();

      adapterManager.register({
        config: {
          type: AdapterType.DATE,
          library: DateLibrary.DAYJS,
          priority: 10,
          features: ['datetime-picker', 'calendar', 'time-formatting'],
          enabled: true,
        },
        factory: () => dayjsAdapter,
      });

      console.log('✅ Dayjs 适配器已注册');
    } catch (error) {
      console.warn('⚠️ Dayjs 适配器注册失败:', error);
    }

    // 注册 Date-fns 适配器
    try {
      const dateFnsAdapter = new DateFnsAdapter();

      adapterManager.register({
        config: {
          type: AdapterType.DATE,
          library: DateLibrary.DATE_FNS,
          priority: 8,
          features: ['task-scheduling', 'countdown', 'relative-time'],
          enabled: true,
        },
        factory: () => dateFnsAdapter,
      });

      console.log('✅ Date-fns 适配器已注册');
    } catch (error) {
      console.warn('⚠️ Date-fns 适配器注册失败:', error);
    }
  }

  /**
   * 设置功能映射
   */
  private static setupFeatureMappings(): void {
    const adapterManager = AdapterManager.getInstance();

    // 拖拽功能映射
    adapterManager.setFeatureMappings({
      'task-list': DragDropLibrary.REACT_DND,
      'vehicle-dispatch': DragDropLibrary.REACT_DND,
      'dispatched-vehicles': DragDropLibrary.REACT_DND,
      'production-lines': DragDropLibrary.REACT_DND,
      'cross-task-transfer': DragDropLibrary.REACT_DND,
      'ratio-design': DragDropLibrary.DND_KIT,
      'card-config': DragDropLibrary.DND_KIT,
      'column-reorder': DragDropLibrary.DND_KIT,
    });

    // 日期功能映射
    adapterManager.setFeatureMappings({
      'datetime-picker': DateLibrary.DAYJS,
      'task-scheduling': DateLibrary.DATE_FNS,
      'countdown': DateLibrary.DATE_FNS,
      'calendar': DateLibrary.DAYJS,
      'time-formatting': DateLibrary.DAYJS,
      'vehicle-dispatch': DateLibrary.DATE_FNS,
    });

    console.log('✅ 功能映射配置完成');
  }

  /**
   * 获取初始化状态
   */
  static isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * 重置适配器系统（仅用于测试）
   */
  static reset(): void {
    if (process.env.NODE_ENV !== 'test') {
      console.warn('⚠️ reset() 方法仅应在测试环境中使用');
      return;
    }
    this.initialized = false;
    console.log('🔄 适配器系统已重置');
  }

  /**
   * 健康检查
   */
  static healthCheck(): { status: 'healthy' | 'error'; details: any } {
    try {
      if (!this.initialized) {
        return {
          status: 'error',
          details: { message: '适配器系统未初始化' }
        };
      }

      const adapterManager = AdapterManager.getInstance();
      const metrics = adapterManager.getPerformanceMetrics();

      // 检查关键适配器是否可用
      const dragAdapter = adapterManager.getAdapter(AdapterType.DRAG_DROP, 'task-list');
      if (!dragAdapter) {
        return {
          status: 'error',
          details: { message: '拖拽适配器不可用' }
        };
      }

      return {
        status: 'healthy',
        details: {
          initialized: this.initialized,
          metrics,
          timestamp: new Date().toISOString(),
        }
      };
    } catch (error) {
      return {
        status: 'error',
        details: { error: error instanceof Error ? error.message : String(error) }
      };
    }
  }
}

/**
 * 自动初始化适配器（在模块加载时）
 */
if (typeof window !== 'undefined') {
  // 浏览器环境下自动初始化
  AdapterRegistry.initialize();
}

/**
 * 导出便捷函数
 */
export const initializeAdapters = () => AdapterRegistry.initialize();
export const isAdaptersInitialized = () => AdapterRegistry.isInitialized();
export const checkAdaptersHealth = () => AdapterRegistry.healthCheck();
