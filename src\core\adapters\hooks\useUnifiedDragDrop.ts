/**
 * 统一拖拽Hook - 业务代码的统一入口
 */

import React, { useMemo } from 'react';
import { AdapterManager, AdapterType } from '../AdapterManager';
import type { 
  UnifiedDragConfig, 
  UnifiedDropConfig, 
  UnifiedDragResult, 
  UnifiedDropResult,
  IDragDropAdapter 
} from '../DragDropAdapter';

/**
 * 统一拖拽Hook
 * @param config 拖拽配置
 * @param feature 功能特性标识，用于选择合适的适配器
 */
export function useUnifiedDrag(
  config: UnifiedDragConfig, 
  feature?: string
): UnifiedDragResult {
  const adapter = useMemo(() => {
    const adapterManager = AdapterManager.getInstance();
    return adapterManager.getAdapter<IDragDropAdapter>(AdapterType.DRAG_DROP, feature);
  }, [feature]);

  return adapter.useDrag(config);
}

/**
 * 统一放置Hook
 * @param config 放置配置
 * @param feature 功能特性标识，用于选择合适的适配器
 */
export function useUnifiedDrop(
  config: UnifiedDropConfig, 
  feature?: string
): UnifiedDropResult {
  const adapter = useMemo(() => {
    const adapterManager = AdapterManager.getInstance();
    return adapterManager.getAdapter<IDragDropAdapter>(AdapterType.DRAG_DROP, feature);
  }, [feature]);

  return adapter.useDrop(config);
}

/**
 * 获取拖拽Provider组件
 * @param feature 功能特性标识
 */
export function useUnifiedDragDropProvider(feature?: string) {
  return useMemo(() => {
    const adapterManager = AdapterManager.getInstance();
    return adapterManager.getProvider(AdapterType.DRAG_DROP, feature);
  }, [feature]);
}

/**
 * 车辆拖拽专用Hook - 针对车辆调度场景优化
 */
export function useVehicleDrag(vehicleId: string, vehicleData: any) {
  return useUnifiedDrag(
    {
      id: vehicleId,
      type: 'vehicle-card',
      data: vehicleData,
    },
    'vehicle-dispatch' // 使用车辆调度特性
  );
}

/**
 * 任务拖拽专用Hook - 针对任务管理场景优化
 */
export function useTaskDrag(taskId: string, taskData: any) {
  return useUnifiedDrag(
    {
      id: taskId,
      type: 'task-card',
      data: taskData,
    },
    'task-list' // 使用任务列表特性
  );
}

/**
 * 生产线放置专用Hook - 针对生产线场景优化
 */
export function useProductionLineDrop(
  onVehicleDrop: (vehicleData: any) => void,
  canAcceptVehicle?: (vehicleData: any) => boolean
) {
  return useUnifiedDrop(
    {
      accept: ['vehicle-card'],
      onDrop: (item) => {
        if (item.data) {
          onVehicleDrop(item.data);
        }
      },
      canDrop: canAcceptVehicle ? (item) => canAcceptVehicle(item.data) : undefined,
    },
    'production-lines' // 使用生产线特性
  );
}

/**
 * 跨任务车辆转移专用Hook
 */
export function useCrossTaskVehicleTransfer(
  onVehicleTransfer: (vehicleData: any, targetTaskId: string) => void
) {
  return useUnifiedDrop(
    {
      accept: ['vehicle-card'],
      onDrop: (item) => {
        if (item.data && item.data['targetTaskId']) {
          onVehicleTransfer(item.data, item.data['targetTaskId']);
        }
      },
    },
    'cross-task-transfer' // 使用跨任务转移特性
  );
}

/**
 * 拖拽状态Hook - 获取全局拖拽状态
 */
export function useDragDropState() {
  // TODO: 实现全局拖拽状态管理
  return {
    isDragging: false,
    draggedItem: null,
    dragType: null,
  };
}

/**
 * 拖拽性能监控Hook
 */
export function useDragDropPerformance() {
  return useMemo(() => {
    const adapterManager = AdapterManager.getInstance();
    return adapterManager.getPerformanceMetrics();
  }, []);
}

/**
 * 高阶组件：为组件添加拖拽功能
 */
export function withDragDrop<T extends Record<string, any>>(
  Component: React.ComponentType<T>,
  dragConfig?: UnifiedDragConfig,
  dropConfig?: UnifiedDropConfig,
  feature?: string
) {
  return React.forwardRef<HTMLElement, T>((props, ref) => {
    const dragResult = dragConfig ? useUnifiedDrag(dragConfig, feature) : null;
    const dropResult = dropConfig ? useUnifiedDrop(dropConfig, feature) : null;

    // 组合ref的逻辑
    const combinedRef = React.useCallback((node: HTMLElement | null) => {
      // 设置拖拽ref
      if (dragResult?.dragRef) {
        dragResult.dragRef(node);
      }

      // 设置放置ref
      if (dropResult?.dropRef) {
        dropResult.dropRef(node);
      }

      // 设置外部ref
      if (typeof ref === 'function') {
        ref(node);
      } else if (ref && 'current' in ref) {
        (ref as React.MutableRefObject<HTMLElement | null>).current = node;
      }
    }, [dragResult, dropResult, ref]);

    // 合并props
    const enhancedProps = {
      ...props,
      ref: combinedRef,
      isDragging: dragResult?.isDragging || false,
      isOver: dropResult?.isOver || false,
      canDrop: dropResult?.canDrop || false,
      // 传递拖拽属性和监听器（用于dnd-kit）
      ...dragResult?.attributes,
      ...dragResult?.listeners,
    } as T;

    return React.createElement(Component, enhancedProps);
  });
}

/**
 * 拖拽调试Hook - 开发环境下的调试工具
 */
export function useDragDropDebug() {
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return useMemo(() => {
    const adapterManager = AdapterManager.getInstance();
    const metrics = adapterManager.getPerformanceMetrics();
    
    return {
      metrics,
      logDragStart: (item: any) => console.log('🚀 拖拽开始:', item),
      logDragEnd: (item: any) => console.log('🎯 拖拽结束:', item),
      logDrop: (item: any, target: any) => console.log('📦 放置完成:', { item, target }),
    };
  }, []);
}
