/**
 * 日期库适配器 - 统一日期处理接口
 * 支持 dayjs 和 date-fns 的可插拔切换
 */

export type DateLibrary = 'dayjs' | 'date-fns';

// 统一的日期对象接口
export interface UnifiedDate {
  readonly value: Date;
  format(pattern: string): string;
  add(amount: number, unit: DateUnit): UnifiedDate;
  subtract(amount: number, unit: DateUnit): UnifiedDate;
  isSame(date: UnifiedDate | Date | string, unit?: DateUnit): boolean;
  isBefore(date: UnifiedDate | Date | string): boolean;
  isAfter(date: UnifiedDate | Date | string): boolean;
  isValid(): boolean;
  toDate(): Date;
  toISOString(): string;
  valueOf(): number;
}

export type DateUnit = 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second' | 'millisecond';

/**
 * 日期适配器抽象类
 */
export abstract class DateAdapter {
  abstract readonly library: DateLibrary;
  
  // 创建日期对象
  abstract create(input?: string | Date | number): UnifiedDate;
  abstract now(): UnifiedDate;
  abstract today(): UnifiedDate;
  
  // 格式化
  abstract format(date: Date | string, pattern: string): string;
  abstract formatDistance(date: Date | string, baseDate?: Date | string): string;
  abstract formatRelative(date: Date | string, baseDate?: Date | string): string;
  
  // 计算
  abstract add(date: Date | string, amount: number, unit: DateUnit): UnifiedDate;
  abstract subtract(date: Date | string, amount: number, unit: DateUnit): UnifiedDate;
  abstract diff(dateLeft: Date | string, dateRight: Date | string, unit?: DateUnit): number;
  
  // 比较
  abstract isSame(dateLeft: Date | string, dateRight: Date | string, unit?: DateUnit): boolean;
  abstract isBefore(dateLeft: Date | string, dateRight: Date | string): boolean;
  abstract isAfter(dateLeft: Date | string, dateRight: Date | string): boolean;
  abstract isValid(date: any): boolean;
  
  // 解析
  abstract parse(dateString: string, format: string): UnifiedDate;
  abstract parseISO(dateString: string): UnifiedDate;

  // 开始/结束
  abstract startOf(date: Date | string, unit: DateUnit): UnifiedDate;
  abstract endOf(date: Date | string, unit: DateUnit): UnifiedDate;

  // 获取/设置
  abstract getYear(date: Date | string): number;
  abstract getMonth(date: Date | string): number;
  abstract getDate(date: Date | string): number;
  abstract getHour(date: Date | string): number;
  abstract getMinute(date: Date | string): number;
  abstract getSecond(date: Date | string): number;

  abstract setYear(date: Date | string, year: number): UnifiedDate;
  abstract setMonth(date: Date | string, month: number): UnifiedDate;
  abstract setDate(date: Date | string, day: number): UnifiedDate;
  abstract setHour(date: Date | string, hour: number): UnifiedDate;
  abstract setMinute(date: Date | string, minute: number): UnifiedDate;
  abstract setSecond(date: Date | string, second: number): UnifiedDate;
}

/**
 * 适配器工厂
 */
export class DateAdapterFactory {
  private static adapters: Map<DateLibrary, DateAdapter> = new Map();
  
  static register(library: DateLibrary, adapter: DateAdapter) {
    this.adapters.set(library, adapter);
  }
  
  static create(library: DateLibrary): DateAdapter {
    const adapter = this.adapters.get(library);
    if (!adapter) {
      throw new Error(`Date adapter for ${library} not found`);
    }
    return adapter;
  }
  
  static getAvailable(): DateLibrary[] {
    return Array.from(this.adapters.keys());
  }
}

/**
 * 统一日期工具函数
 */
export class UnifiedDateUtils {
  private static adapter: DateAdapter;
  
  static setAdapter(library: DateLibrary) {
    this.adapter = DateAdapterFactory.create(library);
  }
  
  static getAdapter(): DateAdapter {
    if (!this.adapter) {
      // 默认使用 dayjs
      this.adapter = DateAdapterFactory.create('dayjs');
    }
    return this.adapter;
  }
  
  // 便捷方法
  static now() {
    return this.getAdapter().now();
  }
  
  static create(input?: string | Date | number) {
    return this.getAdapter().create(input);
  }
  
  static format(date: Date | string, pattern: string) {
    return this.getAdapter().format(date, pattern);
  }
  
  static formatDistance(date: Date | string, baseDate?: Date | string) {
    return this.getAdapter().formatDistance(date, baseDate);
  }
  
  static add(date: Date | string, amount: number, unit: DateUnit) {
    return this.getAdapter().add(date, amount, unit);
  }
  
  static subtract(date: Date | string, amount: number, unit: DateUnit) {
    return this.getAdapter().subtract(date, amount, unit);
  }
  
  static diff(dateLeft: Date | string, dateRight: Date | string, unit?: DateUnit) {
    return this.getAdapter().diff(dateLeft, dateRight, unit);
  }
  
  static isSame(dateLeft: Date | string, dateRight: Date | string, unit?: DateUnit) {
    return this.getAdapter().isSame(dateLeft, dateRight, unit);
  }
  
  static isBefore(dateLeft: Date | string, dateRight: Date | string) {
    return this.getAdapter().isBefore(dateLeft, dateRight);
  }
  
  static isAfter(dateLeft: Date | string, dateRight: Date | string) {
    return this.getAdapter().isAfter(dateLeft, dateRight);
  }
  
  static isValid(date: any) {
    return this.getAdapter().isValid(date);
  }
  
  static parse(dateString: string, format: string) {
    return this.getAdapter().parse(dateString, format);
  }
  
  static parseISO(dateString: string) {
    return this.getAdapter().parseISO(dateString);
  }
}

/**
 * React Hook 集成
 */
export function useDateAdapter(library?: DateLibrary) {
  const adapter = library ? DateAdapterFactory.create(library) : UnifiedDateUtils.getAdapter();

  return {
    now: () => adapter.now(),
    create: (input?: string | Date | number) => adapter.create(input),
    format: (date: Date | string, pattern: string) => adapter.format(date, pattern),
    formatDistance: (date: Date | string, baseDate?: Date | string) =>
      adapter.formatDistance(date, baseDate),
    add: (date: Date | string, amount: number, unit: DateUnit) =>
      adapter.add(date, amount, unit),
    subtract: (date: Date | string, amount: number, unit: DateUnit) =>
      adapter.subtract(date, amount, unit),
    diff: (dateLeft: Date | string, dateRight: Date | string, unit?: DateUnit) =>
      adapter.diff(dateLeft, dateRight, unit),
    isSame: (dateLeft: Date | string, dateRight: Date | string, unit?: DateUnit) =>
      adapter.isSame(dateLeft, dateRight, unit),
    isBefore: (dateLeft: Date | string, dateRight: Date | string) =>
      adapter.isBefore(dateLeft, dateRight),
    isAfter: (dateLeft: Date | string, dateRight: Date | string) =>
      adapter.isAfter(dateLeft, dateRight),
    isValid: (date: any) => adapter.isValid(date),
    parse: (dateString: string, format: string) => adapter.parse(dateString, format),
    parseISO: (dateString: string) => adapter.parseISO(dateString),
  };
}

/**
 * 配置管理
 */
export interface DateConfig {
  defaultLibrary: DateLibrary;
  featureMapping: Record<string, DateLibrary>;
  locale: string;
  defaultFormat: string;
}

export const dateConfig: DateConfig = {
  defaultLibrary: 'dayjs',
  featureMapping: {
    'datetime-picker': 'dayjs',
    'task-scheduling': 'date-fns',
    'countdown': 'date-fns',
    'calendar': 'dayjs',
  },
  locale: 'zh-CN',
  defaultFormat: 'YYYY-MM-DD HH:mm:ss',
};
