/**
 * 简单适配器测试页面
 */

'use client';

import React, { useEffect, useState } from 'react';
import { AdapterManager, AdapterType, DragDropLibrary } from '@/core/adapters/AdapterManager';
import { ReactDndAdapter, ReactDndProviderFactory } from '@/core/adapters/ReactDndAdapterV2';

export default function TestSimpleAdapterPage() {
  const [status, setStatus] = useState<string>('初始化中...');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    try {
      console.log('🚀 开始手动注册适配器...');
      
      // 获取适配器管理器实例
      const adapterManager = AdapterManager.getInstance();
      console.log('✅ 适配器管理器实例获取成功');

      // 创建 React DnD 适配器和Provider工厂
      const reactDndAdapter = new ReactDndAdapter();
      const reactDndProviderFactory = new ReactDndProviderFactory();
      console.log('✅ React DnD 适配器创建成功');

      // 注册适配器
      adapterManager.register({
        config: {
          type: AdapterType.DRAG_DROP,
          library: DragDropLibrary.REACT_DND,
          priority: 10,
          features: ['task-list', 'vehicle-dispatch'],
          enabled: true,
        },
        factory: () => reactDndAdapter,
        provider: reactDndProviderFactory.createProvider(),
      });
      console.log('✅ 适配器注册成功');

      // 设置功能映射
      adapterManager.setFeatureMappings({
        'task-list': DragDropLibrary.REACT_DND,
        'vehicle-dispatch': DragDropLibrary.REACT_DND,
      });
      console.log('✅ 功能映射设置成功');

      // 测试获取适配器
      const adapter = adapterManager.getAdapter(AdapterType.DRAG_DROP, 'task-list');
      console.log('✅ 适配器获取测试:', adapter ? '成功' : '失败');

      // 测试获取Provider
      const provider = adapterManager.getProvider(AdapterType.DRAG_DROP, 'task-list');
      console.log('✅ Provider获取测试:', provider ? '成功' : '失败');

      // 获取性能指标
      const metrics = adapterManager.getPerformanceMetrics();
      console.log('📊 性能指标:', metrics);

      setStatus('✅ 适配器系统初始化成功');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      console.error('❌ 适配器初始化失败:', errorMessage);
      setError(errorMessage);
      setStatus('❌ 初始化失败');
    }
  }, []);

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">🔧 简单适配器测试</h1>
        <p className="text-muted-foreground">
          手动测试适配器系统的基本功能
        </p>
      </div>

      <div className="max-w-2xl mx-auto space-y-4">
        <div className="p-4 border rounded-lg">
          <h3 className="font-semibold mb-2">初始化状态</h3>
          <p className={`text-sm ${error ? 'text-red-600' : 'text-green-600'}`}>
            {status}
          </p>
          {error && (
            <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
              错误详情: {error}
            </div>
          )}
        </div>

        <div className="p-4 border rounded-lg">
          <h3 className="font-semibold mb-2">测试说明</h3>
          <ul className="text-sm space-y-1 text-muted-foreground">
            <li>• 手动创建和注册 React DnD 适配器</li>
            <li>• 测试适配器管理器的基本功能</li>
            <li>• 验证功能映射是否正常工作</li>
            <li>• 检查性能指标收集</li>
          </ul>
        </div>

        <div className="p-4 border rounded-lg">
          <h3 className="font-semibold mb-2">控制台输出</h3>
          <p className="text-sm text-muted-foreground">
            请打开浏览器开发者工具查看详细的初始化日志
          </p>
        </div>
      </div>
    </div>
  );
}
