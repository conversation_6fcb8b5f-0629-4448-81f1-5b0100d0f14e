/**
 * 适配器管理器 - 使用策略模式 + 工厂模式 + 单例模式
 * 统一管理所有适配器的注册、创建和配置
 */

import React from 'react';

// 适配器类型枚举
export enum AdapterType {
  DRAG_DROP = 'dragDrop',
  DATE = 'date',
  STATE = 'state',
}

// 适配器库枚举
export enum DragDropLibrary {
  REACT_DND = 'react-dnd',
  DND_KIT = 'dnd-kit',
}

export enum DateLibrary {
  DAYJS = 'dayjs',
  DATE_FNS = 'date-fns',
}

// 适配器配置接口
export interface AdapterConfig {
  type: AdapterType;
  library: string;
  priority: number;
  features: string[];
  enabled: boolean;
}

// 适配器注册信息
export interface AdapterRegistration {
  config: AdapterConfig;
  factory: () => any;
  provider?: React.ComponentType<{ children: React.ReactNode }>;
}

/**
 * 适配器管理器 - 单例模式
 */
export class AdapterManager {
  private static instance: AdapterManager;
  private adapters: Map<string, AdapterRegistration> = new Map();
  private activeAdapters: Map<AdapterType, AdapterRegistration> = new Map();
  private featureMapping: Map<string, string> = new Map();

  private constructor() {
    this.loadConfiguration();
  }

  static getInstance(): AdapterManager {
    if (!AdapterManager.instance) {
      AdapterManager.instance = new AdapterManager();
    }
    return AdapterManager.instance;
  }

  /**
   * 注册适配器
   */
  register(registration: AdapterRegistration): void {
    const key = `${registration.config.type}-${registration.config.library}`;
    this.adapters.set(key, registration);
    
    // 如果是第一个注册的该类型适配器，或者优先级更高，则设为活跃适配器
    const currentActive = this.activeAdapters.get(registration.config.type);
    if (!currentActive || registration.config.priority > currentActive.config.priority) {
      this.activeAdapters.set(registration.config.type, registration);
    }

    console.log(`✅ 适配器已注册: ${key}`, registration.config);
  }

  /**
   * 获取适配器实例 - 工厂模式
   */
  getAdapter<T>(type: AdapterType, feature?: string): T {
    let registration: AdapterRegistration | undefined;

    // 如果指定了功能特性，优先使用功能映射
    if (feature) {
      const mappedLibrary = this.featureMapping.get(feature);
      if (mappedLibrary) {
        const key = `${type}-${mappedLibrary}`;
        registration = this.adapters.get(key);
      }
    }

    // 如果没有找到特定映射，使用默认活跃适配器
    if (!registration) {
      registration = this.activeAdapters.get(type);
    }

    if (!registration) {
      throw new Error(`No adapter found for type: ${type}`);
    }

    if (!registration.config.enabled) {
      throw new Error(`Adapter ${type}-${registration.config.library} is disabled`);
    }

    return registration.factory();
  }

  /**
   * 获取适配器Provider组件
   */
  getProvider(type: AdapterType, feature?: string): React.ComponentType<{ children: React.ReactNode }> | null {
    let registration: AdapterRegistration | undefined;

    if (feature) {
      const mappedLibrary = this.featureMapping.get(feature);
      if (mappedLibrary) {
        const key = `${type}-${mappedLibrary}`;
        registration = this.adapters.get(key);
      }
    }

    if (!registration) {
      registration = this.activeAdapters.get(type);
    }

    return registration?.provider || null;
  }

  /**
   * 设置功能映射
   */
  setFeatureMapping(feature: string, library: string): void {
    this.featureMapping.set(feature, library);
  }

  /**
   * 批量设置功能映射
   */
  setFeatureMappings(mappings: Record<string, string>): void {
    Object.entries(mappings).forEach(([feature, library]) => {
      this.featureMapping.set(feature, library);
    });
  }

  /**
   * 切换适配器
   */
  switchAdapter(type: AdapterType, library: string): void {
    const key = `${type}-${library}`;
    const registration = this.adapters.get(key);
    
    if (!registration) {
      throw new Error(`Adapter ${key} not found`);
    }

    this.activeAdapters.set(type, registration);
    console.log(`🔄 适配器已切换: ${type} -> ${library}`);
  }

  /**
   * 启用/禁用适配器
   */
  toggleAdapter(type: AdapterType, library: string, enabled: boolean): void {
    const key = `${type}-${library}`;
    const registration = this.adapters.get(key);
    
    if (registration) {
      registration.config.enabled = enabled;
      console.log(`${enabled ? '✅' : '❌'} 适配器${enabled ? '启用' : '禁用'}: ${key}`);
    }
  }

  /**
   * 获取所有已注册的适配器
   */
  getRegisteredAdapters(): AdapterRegistration[] {
    return Array.from(this.adapters.values());
  }

  /**
   * 获取所有活跃适配器
   */
  getActiveAdapters(): Map<AdapterType, AdapterRegistration> {
    return new Map(this.activeAdapters);
  }

  /**
   * 加载配置
   */
  private loadConfiguration(): void {
    // 从环境变量或配置文件加载
    const dragLibrary = process.env['NEXT_PUBLIC_DRAG_LIBRARY'] || DragDropLibrary.REACT_DND;
    const dateLibrary = process.env['NEXT_PUBLIC_DATE_LIBRARY'] || DateLibrary.DAYJS;

    // 设置默认功能映射
    this.setFeatureMappings({
      'task-list': DragDropLibrary.REACT_DND,
      'vehicle-dispatch': DragDropLibrary.REACT_DND,
      'ratio-design': DragDropLibrary.REACT_DND,
      'card-config': DragDropLibrary.REACT_DND,
      'datetime-picker': DateLibrary.DAYJS,
      'task-scheduling': DateLibrary.DATE_FNS,
      'countdown': DateLibrary.DATE_FNS,
      'calendar': DateLibrary.DAYJS,
    });
  }

  /**
   * 性能监控
   */
  getPerformanceMetrics(): Record<string, any> {
    return {
      registeredCount: this.adapters.size,
      activeCount: this.activeAdapters.size,
      featureMappingCount: this.featureMapping.size,
      adapters: Array.from(this.adapters.keys()),
      activeAdapters: Array.from(this.activeAdapters.entries()).map(([type, reg]) => ({
        type,
        library: reg.config.library,
        enabled: reg.config.enabled,
      })),
    };
  }
}

/**
 * 便捷的全局访问函数
 */
export const adapterManager = AdapterManager.getInstance();

/**
 * React Hook 集成
 */
export function useAdapter<T>(type: AdapterType, feature?: string): T {
  return React.useMemo(() => {
    return adapterManager.getAdapter<T>(type, feature);
  }, [type, feature]);
}

/**
 * 适配器Provider Hook
 */
export function useAdapterProvider(type: AdapterType, feature?: string) {
  return React.useMemo(() => {
    return adapterManager.getProvider(type, feature);
  }, [type, feature]);
}
