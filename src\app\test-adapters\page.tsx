/**
 * 适配器系统测试页面
 */

'use client';

import React, { useEffect } from 'react';
import { AdapterTestPanel } from '@/shared/components/dev/AdapterTestPanel';
import { UnifiedDragDropProvider } from '@/core/providers/UnifiedDragDropProvider';
import { initializeAdapters } from '@/core/adapters/AdapterRegistry';

export default function TestAdaptersPage() {
  useEffect(() => {
    // 确保适配器系统已初始化
    console.log('🔧 手动初始化适配器系统...');
    try {
      initializeAdapters();
      console.log('✅ 适配器系统初始化完成');
    } catch (error) {
      console.error('❌ 适配器系统初始化失败:', error);
    }
  }, []);

  return (
    <UnifiedDragDropProvider feature="task-list">
      <div className="container mx-auto p-6 space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-2">🔧 适配器系统测试</h1>
          <p className="text-muted-foreground">
            测试和调试适配器系统的功能和性能
          </p>
        </div>

        <AdapterTestPanel className="max-w-4xl mx-auto" />

        <div className="text-center text-sm text-muted-foreground">
          <p>
            这个页面用于测试适配器系统是否正常工作。
            <br />
            如果看到拖拽功能正常，说明适配器系统已成功集成。
          </p>
        </div>
      </div>
    </UnifiedDragDropProvider>
  );
}
