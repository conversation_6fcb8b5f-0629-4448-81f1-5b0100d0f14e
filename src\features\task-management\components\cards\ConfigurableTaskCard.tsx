'use client';

import React, { memo, useCallback, useMemo } from 'react';

import { useUnifiedDate } from '@/core/adapters/hooks/useUnifiedDate';
import {
  AlertTriangle,
  CheckCircle2,
  Clock,
  Factory,
  MessageCircle,
  Pause,
  Target,
  XCircle,
} from 'lucide-react';
import { useDrop } from 'react-dnd';

import { Badge } from '@/shared/components/badge';
import { Card, CardContent } from '@/shared/components/card';
import { ItemTypes } from '@/core/constants/dndItemTypes';
import { useTaskHighlight } from '@/core/contexts/TaskRowHighlightContext';
import { useTaskSelectionActions } from '@/core/contexts/TaskSelectionContext';
import { useTaskSelection } from '@/core/hooks/useTaskSelection';
import { cn } from '@/core/lib/utils';
import type { InTaskVehicleCardStyle, Task, Vehicle, VehicleDisplayMode } from '@/core/types';
import { FieldStyle, TaskCardConfig } from '@/core/types/taskCardConfig';

import { InTaskVehicleCard } from '../in-task-vehicle-card';
import { TaskMessageModal } from './TaskMessageModal';

/**
 * 生产线拖拽区域组件
 */
interface ProductionLineDropZoneProps {
  lineId: string;
  lineNumber: number;
  taskId: string;
  onDropVehicle?: (vehicle: Vehicle, taskId: string, lineId: string) => void;
}

const ProductionLineDropZone: React.FC<ProductionLineDropZoneProps> = ({
  lineId,
  lineNumber,
  taskId,
  onDropVehicle,
}) => {
  const { setHighlightedTaskId } = useTaskHighlight();

  const [{ isOver, canDrop }, drop] = useDrop<
    { vehicle: Vehicle; index: number; statusList: string; type: string },
    { dropped: boolean },
    { isOver: boolean; canDrop: boolean }
  >({
    accept: ItemTypes.VEHICLE_CARD_DISPATCH,
    drop: (item, monitor) => {
      if (monitor.didDrop()) return { dropped: true };

      if (item.vehicle && onDropVehicle) {
        onDropVehicle(item.vehicle, taskId, lineId);
        return { dropped: true };
      }
      return { dropped: false };
    },
    canDrop: () => true,
    collect: monitor => ({
      isOver: monitor.isOver({ shallow: true }),
      canDrop: monitor.canDrop(),
    }),
    hover: (_, monitor) => {
      if (monitor.isOver({ shallow: true })) {
        setHighlightedTaskId(taskId);
      }
    },
  });

  return (
    <div
      ref={drop as any}
      data-task-id={taskId}
      data-production-line-id={lineId}
      className={cn(
        'w-full h-14 rounded-xl border-2 border-dashed transition-all duration-300',
        'flex items-center justify-between px-4 text-sm font-medium cursor-pointer',
        'transform transition-transform hover:scale-102',
        'relative overflow-hidden',
        isOver && canDrop
          ? 'border-green-400 bg-gradient-to-r from-green-50 to-green-100 text-green-700 scale-105 shadow-xl border-solid'
          : 'border-slate-300 bg-gradient-to-r from-slate-50 to-slate-100 text-slate-600 hover:border-blue-400 hover:from-blue-50 hover:to-blue-100 hover:text-blue-700'
      )}
    >
      {/* 背景动画效果 */}
      {isOver && canDrop && (
        <div className='absolute inset-0 bg-gradient-to-r from-green-200/50 to-green-300/50 animate-pulse' />
      )}

      {/* 内容 */}
      <div className='flex items-center gap-3 relative z-10'>
        <div
          className={cn(
            'w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300',
            isOver && canDrop
              ? 'bg-green-500 shadow-lg scale-110'
              : 'bg-slate-400 hover:bg-blue-500'
          )}
        >
          <Factory className='w-4 h-4 text-white' />
        </div>
        <span className='font-semibold'>生产线 {lineNumber}</span>
      </div>

      {/* 状态指示器 */}
      <div className='flex items-center gap-2 relative z-10'>
        {isOver && canDrop ? (
          <div className='flex items-center gap-1'>
            <div className='w-2 h-2 rounded-full bg-green-500 animate-bounce' />
            <div
              className='w-2 h-2 rounded-full bg-green-500 animate-bounce'
              style={{ animationDelay: '0.1s' }}
            />
            <div
              className='w-2 h-2 rounded-full bg-green-500 animate-bounce'
              style={{ animationDelay: '0.2s' }}
            />
          </div>
        ) : (
          <div className='w-6 h-6 rounded-full border-2 border-dashed border-current opacity-50' />
        )}
      </div>
    </div>
  );
};

/**
 * 可配置任务卡片组件属性
 */
interface ConfigurableTaskCardProps {
  task: Task;
  vehicles: Vehicle[];
  config: TaskCardConfig;
  size?: 'small'; // 只保留 small 规格
  className?: string;
  vehicleDisplayMode?: VehicleDisplayMode;
  inTaskVehicleCardStyles?: InTaskVehicleCardStyle;
  density?: 'compact' | 'normal' | 'loose';
  onTaskContextMenu?: (event: React.MouseEvent, task: Task) => void;
  onTaskDoubleClick?: (task: Task) => void;
  onTaskClick?: (task: Task, event: React.MouseEvent) => void;
  onDropVehicleOnLine?: (vehicle: Vehicle, taskId: string, lineId: string) => void;
  onCancelDispatch?: (vehicleId: string) => void;
  onOpenStyleEditor?: () => void;
  onOpenDeliveryOrderDetails?: (vehicleId: string, taskId: string) => void;
  onOpenVehicleContextMenu?: (event: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
}

/**
 * 获取状态图标和样式
 */
const getStatusInfo = (status: string) => {
  switch (status) {
    case 'ReadyToProduce':
      return {
        icon: Pause,
        label: '待生产',
        className: 'bg-yellow-100 text-yellow-700 border-yellow-200',
      };
    case 'InProgress':
      return {
        icon: Clock,
        label: '进行中',
        className: 'bg-blue-100 text-blue-700 border-blue-200',
      };
    case 'Completed':
      return {
        icon: CheckCircle2,
        label: '已完成',
        className: 'bg-green-100 text-green-700 border-green-200',
      };
    case 'Cancelled':
      return {
        icon: XCircle,
        label: '已取消',
        className: 'bg-red-100 text-red-700 border-red-200',
      };
    default:
      return {
        icon: AlertTriangle,
        label: '未知',
        className: 'bg-gray-100 text-gray-700 border-gray-200',
      };
  }
};

/**
 * 获取字段样式类名
 */
const getFieldStyleClasses = (style: FieldStyle): string => {
  const classes = [];

  // 字体大小
  switch (style.fontSize) {
    case 'xs':
      classes.push('text-xs');
      break;
    case 'sm':
      classes.push('text-sm');
      break;
    case 'base':
      classes.push('text-base');
      break;
    case 'lg':
      classes.push('text-lg');
      break;
    case 'xl':
      classes.push('text-xl');
      break;
  }

  // 字体粗细
  switch (style.fontWeight) {
    case 'normal':
      classes.push('font-normal');
      break;
    case 'medium':
      classes.push('font-medium');
      break;
    case 'semibold':
      classes.push('font-semibold');
      break;
    case 'bold':
      classes.push('font-bold');
      break;
  }

  // 颜色
  switch (style.color) {
    case 'default':
      classes.push('text-foreground');
      break;
    case 'muted':
      classes.push('text-muted-foreground');
      break;
    case 'primary':
      classes.push('text-primary');
      break;
    case 'secondary':
      classes.push('text-secondary');
      break;
    case 'destructive':
      classes.push('text-destructive');
      break;
    case 'warning':
      classes.push('text-warning');
      break;
    case 'success':
      classes.push('text-success');
      break;
  }

  // 对齐方式
  switch (style.textAlign) {
    case 'left':
      classes.push('text-left');
      break;
    case 'center':
      classes.push('text-center');
      break;
    case 'right':
      classes.push('text-right');
      break;
  }

  return classes.join(' ');
};

/**
 * 获取卡片样式类名 - 优化视觉效果
 */
const getCardStyleClasses = (config: TaskCardConfig): string => {
  const classes = ['relative overflow-hidden cursor-pointer border'];

  // 主题 - 增强视觉效果
  switch (config.style.theme) {
    case 'modern':
      classes.push(
        'bg-gradient-to-br from-slate-50 to-slate-100 border-slate-200/60 hover:border-slate-300'
      );
      break;
    case 'glass':
      classes.push('bg-white/90 backdrop-blur-md border-white/30 hover:bg-white/95');
      break;
    case 'gradient':
      classes.push(
        'bg-gradient-to-br from-blue-50 via-white to-purple-50 border-blue-200/60 hover:border-blue-300'
      );
      break;
    case 'dark':
      classes.push('bg-slate-800 border-slate-700 text-white hover:bg-slate-750');
      break;
    default:
      classes.push('bg-white border-gray-200/60 hover:border-gray-300 hover:bg-gray-50/30');
  }

  // 圆角 - 增加默认圆角
  switch (config.style.borderRadius) {
    case 'none':
      classes.push('rounded-none');
      break;
    case 'sm':
      classes.push('rounded-sm');
      break;
    case 'md':
      classes.push('rounded-lg');
      break; // 增强默认圆角
    case 'lg':
      classes.push('rounded-xl');
      break;
    case 'xl':
      classes.push('rounded-2xl');
      break;
  }

  // 阴影 - 增强阴影效果
  switch (config.style.shadow) {
    case 'none':
      classes.push('shadow-none');
      break;
    case 'sm':
      classes.push('shadow-md hover:shadow-lg');
      break; // 增强默认阴影
    case 'md':
      classes.push('shadow-lg hover:shadow-xl');
      break;
    case 'lg':
      classes.push('shadow-xl hover:shadow-2xl');
      break;
    case 'xl':
      classes.push('shadow-2xl hover:shadow-3xl');
      break;
  }

  // 动画 - 优化交互效果
  switch (config.style.animation) {
    case 'subtle':
      classes.push('transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5');
      break;
    case 'smooth':
      classes.push(
        'transition-all duration-300 hover:scale-[1.02] hover:shadow-xl hover:-translate-y-1'
      );
      break;
    case 'bouncy':
      classes.push(
        'transition-all duration-300 hover:scale-105 hover:shadow-2xl hover:rotate-1 hover:-translate-y-1'
      );
      break;
    default:
      classes.push('transition-all duration-200 ease-out hover:shadow-md hover:-translate-y-0.5');
  }

  return classes.join(' ');
};

/**
 * 获取间距类名 - 优化为更紧凑的间距
 */
const getSpacingClasses = (config: TaskCardConfig): string => {
  switch (config.style.spacing) {
    case 'tight':
      return 'p-1 space-y-0.5'; // 更紧凑的间距
    case 'loose':
      return 'p-2 space-y-1.5'; // 适中的间距
    default:
      return 'p-1.5 space-y-1'; // 默认紧凑间距
  }
};

/**
 * 可配置任务卡片组件
 */
export const ConfigurableTaskCard = memo<ConfigurableTaskCardProps>(
  ({
    task,
    vehicles,
    config,
    size: _ = 'small', // 标记为未使用但保留接口兼容性
    className,
    vehicleDisplayMode = 'compact' as VehicleDisplayMode,
    inTaskVehicleCardStyles = {
      gap: 0,
      cardSize: 'small',
      cardWidth: 'w-14',
      cardHeight: 'h-8',
      fontSize: 'text-[10px]',
      fontColor: 'text-foreground',
      vehicleNumberFontWeight: 'font-medium',
      cardBgColor: 'bg-background',
      statusDotSize: 'w-1 h-1',
      borderRadius: 'rounded-sm',
      boxShadow: 'shadow-sm',
    },
    density = 'normal',
    onTaskContextMenu,
    onTaskDoubleClick,
    onTaskClick,
    onDropVehicleOnLine,
    onCancelDispatch,
    onOpenStyleEditor,
    onOpenDeliveryOrderDetails,
    onOpenVehicleContextMenu,
  }) => {
    const [showProductionPanel, setShowProductionPanel] = React.useState(false);
    const [showMessageModal, setShowMessageModal] = React.useState(false);

    // 使用日期适配器
    const dateAdapter = useUnifiedDate(undefined, 'task-scheduling');

    // 任务选择状态 - 使用优化的 hook
    const { isSelected } = useTaskSelection(task.id);
    const { setSelectedTask } = useTaskSelectionActions();

    const statusInfo = useMemo(() => getStatusInfo(task.dispatchStatus), [task.dispatchStatus]);
    const StatusIcon = statusInfo.icon;

    // 动态计算卡片尺寸 - 优化为更紧凑美观的布局
    const getCardDimensions = useMemo(() => {
      // 计算字段数量 - 避免嵌套useMemo
      const currentContentFields = Object.values(config.areas?.content?.fields || {}).filter(
        field => field?.visible !== false
      );
      const currentBottomFields = Object.values(config.areas?.bottom?.fields || {}).filter(
        field => field?.visible !== false
      );

      // 计算内容高度需求 - 更紧凑的计算
      const calculateContentHeight = () => {
        let height = 0;

        // 顶部区域高度 - 减少高度，更紧凑
        if (config.areas?.top?.visible !== false) {
          const baseHeight =
            config.style?.spacing === 'tight' ? 28 : config.style?.spacing === 'loose' ? 40 : 32;
          height += baseHeight;
        }

        // 车辆区域高度 - 减少为75px，更紧凑
        height += 75; // 两行三列车辆区域，优化尺寸

        // 内容区域高度 - 减少行高
        if (config.areas?.content?.visible !== false) {
          const fieldsPerColumn =
            config.areas?.content?.layout === 'double'
              ? Math.ceil(currentContentFields.length / 2)
              : currentContentFields.length;
          const baseRowHeight =
            config.style?.spacing === 'tight' ? 14 : config.style?.spacing === 'loose' ? 22 : 17;
          height += fieldsPerColumn * baseRowHeight;
        }

        // 底部区域高度 - 减少行高
        if (config.areas?.bottom?.visible !== false) {
          const fieldsPerColumn =
            config.areas?.bottom?.layout === 'double'
              ? Math.ceil(currentBottomFields.length / 2)
              : currentBottomFields.length;
          const baseRowHeight =
            config.style?.spacing === 'tight' ? 12 : config.style?.spacing === 'loose' ? 18 : 15;
          height += fieldsPerColumn * baseRowHeight;
        }

        // 内边距 - 减少内边距
        const basePadding =
          config.style?.spacing === 'tight' ? 8 : config.style?.spacing === 'loose' ? 16 : 10;
        height += basePadding;

        return Math.max(height, 180); // 减少最小高度到180px
      };

      // 计算宽度需求 - 优化宽度计算
      const calculateWidth = () => {
        const isDoubleLayout =
          config.areas?.content?.layout === 'double' || config.areas?.bottom?.layout === 'double';

        // 小卡片的基础宽度 - 稍微增加以容纳更多内容
        let baseWidth = isDoubleLayout ? 260 : 220;

        // 根据间距调整
        if (config.style?.spacing === 'tight') {
          baseWidth -= 8;
        } else if (config.style?.spacing === 'loose') {
          baseWidth += 12;
        }

        return Math.max(baseWidth, 180); // 增加最小宽度到180px
      };

      const width = calculateWidth();
      const height = calculateContentHeight();
      return {
        width,
        height,
        className: `w-[${width}px] min-h-[${height}px]`,
      };
    }, [config]);

    // 计算进度百分比
    const progressPercentage = useMemo(() => {
      if (task.requiredVolume === 0) return 0;
      return Math.round((task.completedVolume / task.requiredVolume) * 100);
    }, [task.completedVolume, task.requiredVolume]);

    // 计算发车提醒信息
    const dispatchReminderInfo = useMemo(() => {
      if (task.dispatchStatus !== 'InProgress') return null;
      if (!task.dispatchFrequencyMinutes || task.dispatchFrequencyMinutes <= 0) return null;
      if (!task.nextScheduledDispatchTime) return null;

      const now = new Date();
      const nextDispatchTime = new Date(task.nextScheduledDispatchTime);
      const totalSecondsDiff = dateAdapter.diff(nextDispatchTime, now, 'second');

      if (totalSecondsDiff <= 0) {
        return { text: '立即发车', type: 'urgent' as const, icon: AlertTriangle };
      }

      const minutesDiff = Math.floor(totalSecondsDiff / 60);
      const secondsDiff = totalSecondsDiff % 60;

      if (minutesDiff <= 5) {
        return {
          text: `${minutesDiff}:${secondsDiff.toString().padStart(2, '0')}`,
          type: 'urgent' as const,
          icon: AlertTriangle,
        };
      } else if (minutesDiff <= 15) {
        return { text: `距发:${minutesDiff} m`, type: 'warning' as const, icon: Clock };
      } else {
        return { text: `距发:${minutesDiff} m`, type: 'normal' as const, icon: Clock };
      }
    }, [task.dispatchStatus, task.dispatchFrequencyMinutes, task.nextScheduledDispatchTime, dateAdapter]);

    // 拖拽目标设置
    const [{ isOver, canDrop }, drop] = useDrop<
      { vehicle: Vehicle; index: number; statusList: string; type: string },
      { dropped: boolean },
      { isOver: boolean; canDrop: boolean }
    >({
      accept: ItemTypes.VEHICLE_CARD_DISPATCH,
      drop: (item, monitor) => {
        if (monitor.didDrop()) return { dropped: true };

        // 处理车辆拖拽到任务卡片
        if (item.vehicle && onDropVehicleOnLine) {
          // 默认分配到第一条生产线
          onDropVehicleOnLine(item.vehicle, task.id, 'L1');
          return { dropped: true };
        }
        return { dropped: false };
      },
      canDrop: () => task.dispatchStatus === 'InProgress',
      collect: monitor => ({
        isOver: monitor.isOver({ shallow: true }),
        canDrop: monitor.canDrop(),
      }),
    });

    // 当有拖拽悬停时显示生产线面板 - 优化逻辑防止频繁弹出收起
    React.useEffect(() => {
      if (isOver && canDrop) {
        setShowProductionPanel(true);
      } else if (!isOver && !canDrop) {
        // 只有在完全离开拖拽区域时才隐藏面板
        const timer = setTimeout(() => {
          setShowProductionPanel(false);
        }, 300); // 增加延迟时间
        return () => clearTimeout(timer);
      }
      return undefined;
    }, [isOver, canDrop]);

    // 优化事件处理器
    const handleTaskContextMenu = useCallback(
      (e: React.MouseEvent) => {
        e.preventDefault();
        onTaskContextMenu?.(e, task);
      },
      [onTaskContextMenu, task]
    );

    const handleTaskDoubleClick = useCallback(() => {
      onTaskDoubleClick?.(task);
    }, [onTaskDoubleClick, task]);

    const handleTaskClick = useCallback(
      (e: React.MouseEvent) => {
        // 避免在点击按钮或其他交互元素时触发卡片选中
        const target = e.target as HTMLElement;
        if (target.closest('button, a, input, select, textarea, [role="button"]')) {
          return;
        }

        // 如果有外部点击处理函数，优先使用
        if (onTaskClick) {
          onTaskClick(task, e);
          return;
        }

        // 默认的选中/取消选中逻辑
        if (isSelected) {
          setSelectedTask(null);
        } else {
          setSelectedTask(task);
        }
      },
      [onTaskClick, task, isSelected, setSelectedTask]
    );

    const handleCancelDispatch = useCallback(
      (vehicleId: string) => {
        onCancelDispatch?.(vehicleId);
      },
      [onCancelDispatch]
    );

    const handleOpenDeliveryOrderDetails = useCallback(
      (vehicleId: string) => {
        onOpenDeliveryOrderDetails?.(vehicleId, task.id);
      },
      [onOpenDeliveryOrderDetails, task.id]
    );

    const handleOpenVehicleContextMenu = useCallback(
      (e: React.MouseEvent, vehicle: Vehicle) => {
        onOpenVehicleContextMenu?.(e, vehicle, task);
      },
      [onOpenVehicleContextMenu, task]
    );

    const handleMessageIconClick = useCallback((e: React.MouseEvent) => {
      e.stopPropagation(); // 防止触发卡片点击事件
      setShowMessageModal(true);
    }, []);

    const handleMarkAsRead = useCallback((messageId: string) => {
      // TODO: 实现标记消息为已读的逻辑
      console.log('Mark message as read:', messageId);
    }, []);

    const handleMarkAllAsRead = useCallback((taskId: string) => {
      // TODO: 实现标记所有消息为已读的逻辑
      console.log('Mark all messages as read for task:', taskId);
    }, []);

    // 缓存拖拽样式
    const dragStyles = React.useMemo(() => {
      if (isOver && canDrop) {
        return 'ring-2 ring-primary shadow-lg bg-primary/5';
      }
      return '';
    }, [isOver, canDrop]);

    return (
      <div className={cn('relative z-0 ')} ref={drop as any}>
        <Card
          className={cn(
            getCardStyleClasses(config),
            'flex flex-col task-card task-card-clickable', // 弹性布局 + 卡片样式类
            getCardDimensions.className, // 根据配置动态设置宽度和高度
            dragStyles,
            isSelected && 'task-row-selected', // 选中状态样式
            className
          )}
          onContextMenu={handleTaskContextMenu}
          onDoubleClick={handleTaskDoubleClick}
          onClick={handleTaskClick}
        >
          <CardContent className={cn('flex-1 flex flex-col', getSpacingClasses(config))}>
            {/* 顶部区域 - 优化紧凑布局 */}
            {config.areas.top.visible && (
              <div className='border-b border-border/30 pb-1.5'>
                <div className='flex items-start justify-between gap-2'>
                  {/* 左侧：消息图标 + 项目信息 */}
                  <div className='min-w-0 flex-1 flex items-start gap-1.5'>
                    {/* 消息图标 */}

                    {config.areas?.top?.fields?.messageIcon?.visible && (
                      <div
                        className='relative flex-shrink-0 mt-0.5 cursor-pointer hover:bg-gray-100 rounded p-0.5 -m-0.5 transition-colors'
                        onClick={handleMessageIconClick}
                        title={
                          task.hasNewMessages ? `${task.unreadMessageCount} 条未读消息` : '查看消息'
                        }
                      >
                        <MessageCircle
                          className={cn(
                            'w-3.5 h-3.5',
                            config.areas?.top?.fields?.messageIcon
                              ? getFieldStyleClasses(config.areas.top.fields.messageIcon)
                              : '',
                            task.hasNewMessages ? 'text-blue-600' : 'text-muted-foreground'
                          )}
                        />
                        {/* 消息角标 - 只在有新消息且数量大于0时显示 */}

                        {task.hasNewMessages &&
                          task.unreadMessageCount &&
                          task.unreadMessageCount > 0 && (
                            <div className='absolute -top-0.5 -right-0.5 bg-red-500 text-white text-[9px] font-medium rounded-full min-w-[14px] h-3.5 flex items-center justify-center px-0.5'>
                              {task.unreadMessageCount > 9 ? '9+' : task.unreadMessageCount}
                            </div>
                          )}
                      </div>
                    )}

                    {/* 项目信息 */}
                    <div className='min-w-0 flex-1'>
                      {config.areas.top.fields.projectName.visible && (
                        <div
                          className={cn(
                            'truncate text-sm font-semibold leading-tight',
                            getFieldStyleClasses(config.areas.top.fields.projectName)
                          )}
                        >
                          {task.projectName || task.taskNumber}
                        </div>
                      )}
                      {config.areas.top.fields.constructionSite.visible && (
                        <div
                          className={cn(
                            'truncate text-xs text-muted-foreground mt-0.5 leading-tight',
                            getFieldStyleClasses(config.areas.top.fields.constructionSite)
                          )}
                        >
                          {task.constructionSite}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 右侧：发车提醒 + 强度 分两行显示，位置固定 */}
                  <div className='flex flex-col gap-0.5 flex-shrink-0 items-end w-18'>
                    {/* 第一行：发车提醒 - 固定位置，无数据时占位 */}

                    <div className='h-3.5 flex items-center justify-end'>
                      {config.areas.top.fields.dispatchReminder.visible && dispatchReminderInfo ? (
                        <div
                          className={cn(
                            'text-[10px] cursor-help flex items-center gap-0.5',
                            getFieldStyleClasses(config.areas.top.fields.dispatchReminder),
                            dispatchReminderInfo.type === 'urgent'
                              ? 'text-red-600'
                              : dispatchReminderInfo.type === 'warning'
                                ? 'text-yellow-600'
                                : 'text-muted-foreground'
                          )}
                          title={`下次发车时间: ${task.nextScheduledDispatchTime ? new Date(task.nextScheduledDispatchTime).toLocaleString() : '未计划'}`}
                        >
                          {React.createElement(dispatchReminderInfo.icon, {
                            className: 'w-2.5 h-2.5',
                          })}
                          <span>{dispatchReminderInfo.text}</span>
                        </div>
                      ) : config.areas.top.fields.dispatchReminder.visible ? (
                        <div className='text-[10px] text-transparent'>占位</div>
                      ) : null}
                    </div>

                    {/* 第二行：强度 - 固定位置，加粗高亮显示 */}
                    <div className='h-3.5 flex items-center justify-end'>
                      {config.areas.top.fields.strength.visible && (
                        <div
                          className={cn(
                            'text-[11px] text-orange-600 flex items-center gap-0.5 font-bold bg-orange-50 px-1.5 py-0.5 rounded-md border border-orange-200 shadow-sm',
                            getFieldStyleClasses(config.areas.top.fields.strength)
                          )}
                        >
                          <Target className='w-2.5 h-2.5 text-orange-700' />
                          <span className='font-bold text-orange-700'>{task.strength}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 调度车辆区域 - 优化紧凑布局 */}
            <div className='border-b border-border/30 pb-1.5'>
              <div className='h-[75px] bg-gradient-to-br from-slate-50 to-slate-100 border border-slate-200/60 rounded-md p-1.5 overflow-y-auto'>
                {vehicles.length > 0 ? (
                  <div className='grid auto-cols-max grid-cols-3 gap-0.5 auto-rows-max'>
                    {vehicles.map(vehicle => (
                      <InTaskVehicleCard
                        key={vehicle.id}
                        vehicle={vehicle}
                        task={task}
                        vehicleDisplayMode={vehicleDisplayMode}
                        inTaskVehicleCardStyles={{
                          ...inTaskVehicleCardStyles,
                          gap: 0,
                          cardSize: 'small',
                          cardWidth: 'w-12',
                          cardHeight: 'h-7',
                          fontSize: 'text-[9px]',
                          fontColor: 'text-foreground',
                          vehicleNumberFontWeight: 'font-medium',
                          cardBgColor: 'bg-white',
                          statusDotSize: 'w-1 h-1',
                          borderRadius: 'rounded',
                          boxShadow: 'shadow-sm',
                        }}
                        productionLineCount={task.productionLineCount || 1}
                        density={density}
                        onCancelDispatch={handleCancelDispatch}
                        onOpenStyleEditor={onOpenStyleEditor}
                        onOpenDeliveryOrderDetails={handleOpenDeliveryOrderDetails}
                        onOpenContextMenu={handleOpenVehicleContextMenu}
                      />
                    ))}
                  </div>
                ) : (
                  <div className='flex items-center justify-center h-full'>
                    <span className='text-xs text-slate-400'>已调度车辆</span>
                  </div>
                )}
              </div>
            </div>

            {/* 内容区域 - 优化紧凑双列布局 */}
            {config.areas.content.visible && (
              <div className='flex-1 border-b border-border/30 py-1.5'>
                {config.areas.content.layout === 'double' ? (
                  /* 双列布局 - 左右分栏 */
                  <div className='grid grid-cols-2 gap-2'>
                    {/* 左列 */}
                    <div className='space-y-0.5 pr-1.5 border-r border-border/20'>
                      {config.areas.content.fields.requiredVolume.visible && (
                        <div className='flex flex-col text-xs py-0'>
                          <span className='text-muted-foreground text-[9px] leading-tight'>
                            需求方量
                          </span>
                          <span
                            className={cn(
                              'font-medium text-[11px] leading-tight',
                              getFieldStyleClasses(config.areas.content.fields.requiredVolume)
                            )}
                          >
                            {task.requiredVolume}m³
                          </span>
                        </div>
                      )}
                      {config.areas.content.fields.completedVolume.visible && (
                        <div className='flex flex-col text-xs py-0'>
                          <span className='text-muted-foreground text-[9px] leading-tight'>
                            完成方量
                          </span>
                          <span
                            className={cn(
                              'font-medium text-[11px] leading-tight',
                              getFieldStyleClasses(config.areas.content.fields.completedVolume)
                            )}
                          >
                            {task.completedVolume}m³
                          </span>
                        </div>
                      )}
                      {config.areas.content.fields.scheduledTime.visible && (
                        <div className='flex flex-col text-xs py-0'>
                          <span className='text-muted-foreground text-[9px] leading-tight'>
                            计划时间
                          </span>
                          <span
                            className={cn(
                              'font-medium text-[11px] leading-tight',
                              getFieldStyleClasses(config.areas.content.fields.scheduledTime)
                            )}
                          >
                            {task.scheduledTime || '--'}
                          </span>
                        </div>
                      )}
                      {config.areas.content.fields.contactPhone.visible && (
                        <div className='flex flex-col text-xs py-0'>
                          <span className='text-muted-foreground text-[9px] leading-tight'>
                            联系电话
                          </span>
                          <span
                            className={cn(
                              'font-medium text-[11px] leading-tight',
                              getFieldStyleClasses(config.areas.content.fields.contactPhone)
                            )}
                          >
                            {task.contactPhone || '--'}
                          </span>
                        </div>
                      )}
                    </div>

                    {/* 右列 */}
                    <div className='space-y-0.5 pl-1.5'>
                      {config.areas.content.fields.completedProgress.visible && (
                        <div className='flex flex-col text-xs py-0'>
                          <span className='text-muted-foreground text-[9px] leading-tight'>
                            完成进度
                          </span>
                          <span
                            className={cn(
                              'font-medium text-[11px] leading-tight',
                              getFieldStyleClasses(config.areas.content.fields.completedProgress)
                            )}
                          >
                            {progressPercentage}%
                          </span>
                        </div>
                      )}
                      {config.areas.content.fields.estimatedDuration.visible && (
                        <div className='flex flex-col text-xs py-0'>
                          <span className='text-muted-foreground text-[9px] leading-tight'>
                            预计时长
                          </span>
                          <span
                            className={cn(
                              'font-medium text-[11px] leading-tight',
                              getFieldStyleClasses(config.areas.content.fields.estimatedDuration)
                            )}
                          >
                            --
                          </span>
                        </div>
                      )}
                      {config.areas.content.fields.constructionLocation.visible && (
                        <div className='flex flex-col text-xs py-0'>
                          <span className='text-muted-foreground text-[9px] leading-tight'>
                            施工地点
                          </span>
                          <span
                            className={cn(
                              'font-medium text-[11px] leading-tight truncate',
                              getFieldStyleClasses(config.areas.content.fields.constructionLocation)
                            )}
                          >
                            {task.constructionLocation || task.constructionSite || '--'}
                          </span>
                        </div>
                      )}
                      {config.areas.content.fields.taskStatus.visible && (
                        <div className='flex flex-col text-xs py-0'>
                          <span className='text-muted-foreground text-[9px] leading-tight'>
                            状态
                          </span>
                          <Badge
                            className={cn(
                              'text-[10px] px-1 py-0 mt-0.5 h-4',
                              getFieldStyleClasses(config.areas.content.fields.taskStatus),
                              statusInfo.className
                            )}
                          >
                            <StatusIcon className='w-2 h-2 mr-0.5' />
                            {statusInfo.label}
                          </Badge>
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  /* 单列布局 - 优化紧凑样式 */
                  <div className='space-y-0.5'>
                    {config.areas.content.fields.requiredVolume.visible && (
                      <div className='flex items-center justify-between text-xs py-0'>
                        <span className='text-muted-foreground text-[9px]'>需求方量</span>
                        <span
                          className={cn(
                            'font-medium text-[11px]',
                            getFieldStyleClasses(config.areas.content.fields.requiredVolume)
                          )}
                        >
                          {task.requiredVolume}m³
                        </span>
                      </div>
                    )}
                    {config.areas.content.fields.completedVolume.visible && (
                      <div className='flex items-center justify-between text-xs py-0'>
                        <span className='text-muted-foreground text-[9px]'>完成方量</span>
                        <span
                          className={cn(
                            'font-medium text-[11px]',
                            getFieldStyleClasses(config.areas.content.fields.completedVolume)
                          )}
                        >
                          {task.completedVolume}m³
                        </span>
                      </div>
                    )}
                    {config.areas.content.fields.scheduledTime.visible && (
                      <div className='flex items-center justify-between text-xs py-0'>
                        <span className='text-muted-foreground text-[9px]'>计划时间</span>
                        <span
                          className={cn(
                            'font-medium text-[11px]',
                            getFieldStyleClasses(config.areas.content.fields.scheduledTime)
                          )}
                        >
                          {task.scheduledTime || '--'}
                        </span>
                      </div>
                    )}
                    {config.areas.content.fields.contactPhone.visible && (
                      <div className='flex items-center justify-between text-xs py-0'>
                        <span className='text-muted-foreground text-[9px]'>联系电话</span>
                        <span
                          className={cn(
                            'font-medium text-[11px]',
                            getFieldStyleClasses(config.areas.content.fields.contactPhone)
                          )}
                        >
                          {task.contactPhone || '--'}
                        </span>
                      </div>
                    )}
                    {config.areas.content.fields.completedProgress.visible && (
                      <div className='flex items-center justify-between text-xs py-0'>
                        <span className='text-muted-foreground text-[9px]'>完成进度</span>
                        <span
                          className={cn(
                            'font-medium text-[11px]',
                            getFieldStyleClasses(config.areas.content.fields.completedProgress)
                          )}
                        >
                          {progressPercentage}%
                        </span>
                      </div>
                    )}
                    {config.areas.content.fields.estimatedDuration.visible && (
                      <div className='flex items-center justify-between text-xs py-0'>
                        <span className='text-muted-foreground text-[9px]'>预计时长</span>
                        <span
                          className={cn(
                            'font-medium text-[11px]',
                            getFieldStyleClasses(config.areas.content.fields.estimatedDuration)
                          )}
                        >
                          --
                        </span>
                      </div>
                    )}
                    {config.areas.content.fields.constructionLocation.visible && (
                      <div className='flex items-center justify-between text-xs py-0'>
                        <span className='text-muted-foreground text-[9px]'>施工地点</span>
                        <span
                          className={cn(
                            'font-medium text-[11px] truncate max-w-[120px]',
                            getFieldStyleClasses(config.areas.content.fields.constructionLocation)
                          )}
                        >
                          {task.constructionSite}
                        </span>
                      </div>
                    )}
                    {config.areas.content.fields.taskStatus.visible && (
                      <div className='flex items-center justify-between text-xs py-0'>
                        <span className='text-muted-foreground text-[9px]'>状态</span>
                        <Badge
                          className={cn(
                            'text-[10px] px-1 py-0 h-4',
                            getFieldStyleClasses(config.areas.content.fields.taskStatus),
                            statusInfo.className
                          )}
                        >
                          <StatusIcon className='w-2 h-2 mr-0.5' />
                          {statusInfo.label}
                        </Badge>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

            {/* 底部区域 - 极简紧凑布局 */}
            {config.areas.bottom.visible && (
              <div className='pt-1 border-t border-border/20 bg-gradient-to-r from-slate-50/50 to-slate-100/50 -mx-1.5 -mb-1.5 px-1.5 pb-1'>
                <div
                  className={cn(
                    'grid gap-0.5 text-xs',
                    config.areas.bottom.layout === 'double' ? 'grid-cols-2' : 'grid-cols-1'
                  )}
                >
                  {/* 左列或单列 */}
                  <div className='space-y-0'>
                    {config.areas.bottom.fields.customerName.visible && (
                      <div className='flex items-center justify-between'>
                        <span className='text-muted-foreground text-[9px]'>客户</span>
                        <span
                          className={cn(
                            'font-medium text-[10px] truncate max-w-[80px]',
                            getFieldStyleClasses(config.areas.bottom.fields.customerName)
                          )}
                        >
                          {task.customerName}
                        </span>
                      </div>
                    )}
                    {config.areas.bottom.fields.createdAt.visible && (
                      <div className='flex items-center justify-between'>
                        <span className='text-muted-foreground text-[9px]'>创建</span>
                        <span
                          className={cn(
                            'font-medium text-[10px]',
                            getFieldStyleClasses(config.areas.bottom.fields.createdAt)
                          )}
                        >
                          {task.createdAt || '--'}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* 右列（仅在双列布局时显示） */}
                  {config.areas.bottom.layout === 'double' && (
                    <div className='space-y-0'>
                      {config.areas.bottom.fields.taskNumber.visible && (
                        <div className='flex items-center justify-between'>
                          <span className='text-muted-foreground text-[9px]'>编号</span>
                          <span
                            className={cn(
                              'font-medium text-[10px] truncate max-w-[80px]',
                              getFieldStyleClasses(config.areas.bottom.fields.taskNumber)
                            )}
                          >
                            {task.taskNumber}
                          </span>
                        </div>
                      )}
                      {config.areas.bottom.fields.updatedAt.visible && (
                        <div className='flex items-center justify-between'>
                          <span className='text-muted-foreground text-[9px]'>更新</span>
                          <span
                            className={cn(
                              'font-medium text-[10px]',
                              getFieldStyleClasses(config.areas.bottom.fields.updatedAt)
                            )}
                          >
                            {task.updatedAt || task.createdAt || '--'}
                          </span>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* 生产线面板 - 半透明背景遮罩 */}
        {showProductionPanel && (
          <div
            className='absolute inset-0 bg-black/20 backdrop-blur-sm rounded-lg'
            style={{ zIndex: 5 }}
          />
        )}

        {/* 生产线面板 */}
        <div
          className={cn(
            'absolute top-0 right-0 h-full max-w-[180px] bg-white/95 backdrop-blur-md',
            'border-l-2 border-blue-200 shadow-2xl rounded-r-lg',
            'transform transition-all duration-500 ease-in-out',
            'translate-x-full opacity-0',
            showProductionPanel && 'translate-x-0 opacity-100'
          )}
          style={{ zIndex: 10 }}
        >
          <div className='p-2 h-full flex flex-col '>
            {/* 生产线列表 */}
            <div className='flex-1 space-y-2'>
              {Array.from({ length: task.productionLineCount || 3 }).map((_, index) => {
                const lineId = `L${index + 1}`;
                return (
                  <ProductionLineDropZone
                    key={index}
                    lineId={lineId}
                    lineNumber={index + 1}
                    taskId={task.id}
                    onDropVehicle={onDropVehicleOnLine}
                  />
                );
              })}
            </div>

            {/* 底部提示 */}
            <div className='mt-6 pt-4 border-t-2 border-blue-100'>
              <div className='flex items-center gap-2 text-sm text-gray-600'>
                <div className='w-2 h-2 rounded-full bg-blue-400 animate-pulse'></div>
                <span>拖拽车辆到生产线完成发车</span>
              </div>
              <div className='text-xs text-gray-400 mt-1'>工程名称：{task.projectName}</div>
            </div>
          </div>
        </div>

        {/* 消息弹窗 */}
        <TaskMessageModal
          open={showMessageModal}
          onOpenChangeAction={setShowMessageModal}
          task={task}
          onMarkAsReadAction={handleMarkAsRead}
          onMarkAllAsReadAction={handleMarkAllAsRead}
        />
      </div>
    );
  },
  (prevProps, nextProps) => {
    // 优化的比较函数，减少不必要的重新渲染

    // 首先检查任务基本信息
    if (
      prevProps.task.id !== nextProps.task.id ||
      prevProps.task.taskNumber !== nextProps.task.taskNumber ||
      prevProps.task.projectName !== nextProps.task.projectName ||
      prevProps.task.dispatchStatus !== nextProps.task.dispatchStatus ||
      prevProps.task.completedVolume !== nextProps.task.completedVolume ||
      prevProps.task.requiredVolume !== nextProps.task.requiredVolume ||
      prevProps.task.isDueForDispatch !== nextProps.task.isDueForDispatch
    ) {
      return false;
    }

    // 检查车辆数据变化
    if (prevProps.vehicles.length !== nextProps.vehicles.length) {
      return false;
    }

    // 只检查车辆的关键属性
    for (let i = 0; i < prevProps.vehicles.length; i++) {
      const prevVehicle = prevProps.vehicles[i];
      const nextVehicle = nextProps.vehicles[i];
      if (
        prevVehicle?.id !== nextVehicle?.id ||
        prevVehicle?.status !== nextVehicle?.status ||
        prevVehicle?.vehicleNumber !== nextVehicle?.vehicleNumber
      ) {
        return false;
      }
    }

    // 检查其他属性
    if (
      prevProps.vehicleDisplayMode !== nextProps.vehicleDisplayMode ||
      prevProps.density !== nextProps.density
    ) {
      return false;
    }

    // 简化配置比较 - 只比较关键配置
    if (
      prevProps.config?.style?.theme !== nextProps.config?.style?.theme ||
      prevProps.config?.style?.spacing !== nextProps.config?.style?.spacing
    ) {
      return false;
    }

    return true;
  }
);

ConfigurableTaskCard.displayName = 'ConfigurableTaskCard';
